package com.fx.broker.http;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;


/**
 * Broker http
 *
 * <AUTHOR>
 */
//@EnableCustomSwagger2
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class}, scanBasePackages = "com.fx")
public class FxBrokerHttpApplication {
    public static void main(String[] args) {
        SpringApplication.run(FxBrokerHttpApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  Broker-Http 模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
