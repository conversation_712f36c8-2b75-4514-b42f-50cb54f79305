package com.fx.common.core.enums;

/**
 * 认证方式（1 一机一密  2一型一密 3不认证）
 * <AUTHOR>
 * @since 2024/5/13 上午11:22
 */
public enum DeviceAuthMethodEnum {

    ONE_MACHINE_ONE_SECRET("1", "一机一密"),
    ONE_MODEL_ONE_SECRET("2", "一型一密"),
    NO_AUTHENTICATION("3", "不认证");

    private final String code;
    private final String info;

    DeviceAuthMethodEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static DeviceAuthMethodEnum fromCode(String code) {
        for (DeviceAuthMethodEnum authMethod : values()) {
            if (authMethod.code.equals(code)) {
                return authMethod;
            }
        }
        throw new IllegalArgumentException("Invalid DeviceAuthMethodEnum code: " + code);
    }

    public static DeviceAuthMethodEnum fromInfo(String info) {
        for (DeviceAuthMethodEnum authMethod : values()) {
            if (authMethod.info.equals(info)) {
                return authMethod;
            }
        }
        throw new IllegalArgumentException("Invalid DeviceAuthMethodEnum info: " + info);
    }

    public static boolean isValidCode(String code) {
        for (DeviceAuthMethodEnum authMethod : values()) {
            if (authMethod.code.equals(code)) {
                return true;
            }
        }
        return false;
    }
}
