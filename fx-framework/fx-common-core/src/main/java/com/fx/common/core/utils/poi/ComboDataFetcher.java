package com.fx.common.core.utils.poi;

/**
 * 下拉框数据源接口
 * <p>
 * 用于获取下拉框数据
 * </p>
 * <p>
 * 例如: 从数据库中获取数据
 * </p>
 * <p>
 * 例如: 从字典表中获取数据
 * </p>
 * <p>
 * 例如: 从配置文件中获取数据
 * </p>
 * <p>
 * 例如: 从枚举中获取数据
 * </p>
 * <p>
 * 例如: 从缓存中获取数据
 * </p>
 * <p>
 * 例如: 从接口中
 * </p>
 * <p>
 * 例如: 从接口中获取数据
 * </p>
 */
public interface ComboDataFetcher {

    /**
     * 获取下拉框数据
     *
     * @return 下拉框数据
     */
    String[] fetch();

}