package com.fx.common.swagger.utils;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OpenAPI 工具类
 * 参考 yudao-cloud 实现，提供便捷的 OpenAPI 配置方法
 * 
 * <AUTHOR>
 */
public class OpenApiUtils {

    /**
     * 创建基础 OpenAPI 配置
     */
    public static OpenAPI createOpenAPI(String title, String description, String version) {
        return createOpenAPI(title, description, version, null, null);
    }

    /**
     * 创建完整 OpenAPI 配置
     */
    public static OpenAPI createOpenAPI(String title, String description, String version, 
                                       Contact contact, License license) {
        OpenAPI openAPI = new OpenAPI()
                .info(createInfo(title, description, version, contact, license))
                .components(new io.swagger.v3.oas.models.Components()
                        .securitySchemes(createSecuritySchemes()));

        // 添加安全要求
        openAPI.addSecurityItem(new SecurityRequirement().addList("Authorization"));
        openAPI.addSecurityItem(new SecurityRequirement().addList("TenantId"));

        return openAPI;
    }

    /**
     * 创建 API 信息
     */
    public static Info createInfo(String title, String description, String version, 
                                 Contact contact, License license) {
        Info info = new Info()
                .title(StringUtils.hasText(title) ? title : "API 文档")
                .description(StringUtils.hasText(description) ? description : "API 接口文档")
                .version(StringUtils.hasText(version) ? version : "1.0");

        if (contact != null) {
            info.setContact(contact);
        }

        if (license != null) {
            info.setLicense(license);
        }

        return info;
    }

    /**
     * 创建联系人信息
     */
    public static Contact createContact(String name, String email, String url) {
        Contact contact = new Contact();
        if (StringUtils.hasText(name)) {
            contact.setName(name);
        }
        if (StringUtils.hasText(email)) {
            contact.setEmail(email);
        }
        if (StringUtils.hasText(url)) {
            contact.setUrl(url);
        }
        return contact;
    }

    /**
     * 创建许可证信息
     */
    public static License createLicense(String name, String url) {
        License license = new License();
        if (StringUtils.hasText(name)) {
            license.setName(name);
        }
        if (StringUtils.hasText(url)) {
            license.setUrl(url);
        }
        return license;
    }

    /**
     * 创建安全认证配置
     */
    public static Map<String, SecurityScheme> createSecuritySchemes() {
        Map<String, SecurityScheme> securitySchemes = new HashMap<>();

        // JWT Bearer Token 认证
        securitySchemes.put("Authorization", new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .scheme("bearer")
                .bearerFormat("JWT")
                .name("Authorization")
                .description("JWT 认证，格式：Bearer {token}"));

        // API Key 认证
        securitySchemes.put("ApiKey", new SecurityScheme()
                .type(SecurityScheme.Type.APIKEY)
                .in(SecurityScheme.In.HEADER)
                .name("X-API-KEY")
                .description("API Key 认证"));

        // 租户认证
        securitySchemes.put("TenantId", new SecurityScheme()
                .type(SecurityScheme.Type.APIKEY)
                .in(SecurityScheme.In.HEADER)
                .name("tenant-id")
                .description("租户标识"));

        return securitySchemes;
    }

    /**
     * 创建服务器配置
     */
    public static List<Server> createServers(String... urls) {
        List<Server> servers = new ArrayList<>();
        for (String url : urls) {
            if (StringUtils.hasText(url)) {
                Server server = new Server();
                server.setUrl(url);
                server.setDescription("API 服务器");
                servers.add(server);
            }
        }
        return servers;
    }

    /**
     * 创建分组 API 文档
     */
    public static GroupedOpenApi createGroupedOpenApi(String group, String displayName, 
                                                     String... pathsToMatch) {
        return GroupedOpenApi.builder()
                .group(group)
                .displayName(displayName)
                .pathsToMatch(pathsToMatch.length > 0 ? pathsToMatch : new String[]{"/**"})
                .pathsToExclude("/error", "/actuator/**")
                .build();
    }

    /**
     * 创建模块分组 API 文档
     * 参考 yudao-cloud 的模块化设计
     */
    public static GroupedOpenApi createModuleGroupedOpenApi(String module) {
        return createModuleGroupedOpenApi(module, getModuleDisplayName(module));
    }

    /**
     * 创建模块分组 API 文档（自定义显示名称）
     */
    public static GroupedOpenApi createModuleGroupedOpenApi(String module, String displayName) {
        return GroupedOpenApi.builder()
                .group(module)
                .displayName(displayName)
                .pathsToMatch("/" + module + "/**")
                .pathsToExclude("/error", "/actuator/**")
                .build();
    }

    /**
     * 获取模块显示名称
     */
    private static String getModuleDisplayName(String module) {
        switch (module) {
            case "system":
                return "系统管理";
            case "link":
                return "设备管理";
            case "tdengine":
                return "时序数据";
            case "monitor":
                return "系统监控";
            case "gen":
                return "代码生成";
            case "auth":
                return "认证授权";
            default:
                return module.toUpperCase() + " 模块";
        }
    }

    /**
     * 创建 fx-links 标准配置
     */
    public static OpenAPI createFxLinksOpenAPI(String moduleName) {
        return createOpenAPI(
                moduleName + " API 文档",
                moduleName + " 模块 API 接口文档",
                "1.0",
                createContact("fx-links 开发团队", "<EMAIL>", "https://fx-links.com"),
                createLicense("Apache 2.0", "https://www.apache.org/licenses/LICENSE-2.0")
        );
    }

    /**
     * 创建开发环境配置
     */
    public static OpenAPI createDevOpenAPI(String moduleName, String serverUrl) {
        OpenAPI openAPI = createFxLinksOpenAPI(moduleName);
        if (StringUtils.hasText(serverUrl)) {
            openAPI.setServers(createServers(serverUrl));
        }
        return openAPI;
    }

    /**
     * 创建生产环境配置（简化版）
     */
    public static OpenAPI createProdOpenAPI(String moduleName) {
        return new OpenAPI()
                .info(new Info()
                        .title(moduleName + " API")
                        .version("1.0"));
    }

    /**
     * 判断是否为生产环境
     */
    public static boolean isProductionEnvironment(String profile) {
        return "prod".equals(profile) || "production".equals(profile);
    }

    /**
     * 根据环境创建 OpenAPI 配置
     */
    public static OpenAPI createOpenAPIByEnvironment(String moduleName, String profile, String serverUrl) {
        if (isProductionEnvironment(profile)) {
            return createProdOpenAPI(moduleName);
        } else {
            return createDevOpenAPI(moduleName, serverUrl);
        }
    }
}
