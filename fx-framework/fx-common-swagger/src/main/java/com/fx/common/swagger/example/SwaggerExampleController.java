package com.fx.common.swagger.example;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * Swagger OpenAPI 3 注解使用示例
 * 参考 yudao-cloud 最佳实践
 *
 * <AUTHOR>
 */
@Tag(name = "示例接口", description = "展示 OpenAPI 3 注解的正确使用方式")
@RestController
@RequestMapping("/example")
@Validated
public class SwaggerExampleController {

    @Operation(
        summary = "创建用户",
        description = "创建新的用户信息，支持批量创建"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功",
                    content = @Content(schema = @Schema(implementation = ApiResult.class))),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "系统错误")
    })
    @PostMapping("/users")
    public ApiResult<Long> createUser(
        @Parameter(description = "用户信息", required = true)
        @Valid @RequestBody UserCreateRequest request) {
        // 业务逻辑
        return ApiResult.success(1L);
    }

    @Operation(summary = "获取用户详情", description = "根据用户ID获取用户详细信息")
    @Parameters({
        @Parameter(name = "id", description = "用户ID", required = true, example = "1"),
        @Parameter(name = "includeRoles", description = "是否包含角色信息", example = "true")
    })
    @GetMapping("/users/{id}")
    public ApiResult<UserResponse> getUser(
        @PathVariable("id") @NotNull Long id,
        @RequestParam(value = "includeRoles", defaultValue = "false") Boolean includeRoles) {
        // 业务逻辑
        return ApiResult.success(new UserResponse());
    }

    @Operation(summary = "用户列表查询", description = "分页查询用户列表，支持多条件筛选")
    @GetMapping("/users")
    public ApiResult<PageResult<UserResponse>> getUserList(
        @Parameter(description = "查询条件") UserQueryRequest query) {
        // 业务逻辑
        return ApiResult.success(new PageResult<>(List.of(), 0L));
    }

    @Operation(summary = "更新用户", description = "更新用户信息")
    @PutMapping("/users/{id}")
    public ApiResult<Void> updateUser(
        @Parameter(description = "用户ID", required = true) @PathVariable Long id,
        @Parameter(description = "更新的用户信息", required = true)
        @Valid @RequestBody UserUpdateRequest request) {
        // 业务逻辑
        return ApiResult.success();
    }

    @Operation(summary = "删除用户", description = "根据ID删除用户")
    @DeleteMapping("/users/{id}")
    public ApiResult<Void> deleteUser(
        @Parameter(description = "用户ID", required = true) @PathVariable Long id) {
        // 业务逻辑
        return ApiResult.success();
    }

    @Operation(summary = "批量删除用户", description = "根据ID列表批量删除用户")
    @DeleteMapping("/users")
    public ApiResult<Void> batchDeleteUsers(
        @Parameter(description = "用户ID列表", required = true)
        @RequestBody List<Long> ids) {
        // 业务逻辑
        return ApiResult.success();
    }

    // ========== 内部类：请求和响应对象 ==========

    /**
     * 用户创建请求
     */
    @Schema(description = "用户创建请求")
    public static class UserCreateRequest {
        
        @Schema(description = "用户名", required = true, example = "admin")
        private String username;
        
        @Schema(description = "密码", required = true, example = "123456")
        private String password;
        
        @Schema(description = "邮箱", example = "<EMAIL>")
        private String email;
        
        @Schema(description = "手机号", example = "13800138000")
        private String phone;
        
        @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
        private Integer status;

        // getters and setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }
    }

    /**
     * 用户更新请求
     */
    @Schema(description = "用户更新请求")
    public static class UserUpdateRequest {
        
        @Schema(description = "邮箱", example = "<EMAIL>")
        private String email;
        
        @Schema(description = "手机号", example = "13800138000")
        private String phone;
        
        @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
        private Integer status;

        // getters and setters
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }
    }

    /**
     * 用户查询请求
     */
    @Schema(description = "用户查询请求")
    public static class UserQueryRequest {
        
        @Schema(description = "用户名", example = "admin")
        private String username;
        
        @Schema(description = "邮箱", example = "<EMAIL>")
        private String email;
        
        @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
        private Integer status;
        
        @Schema(description = "页码", example = "1")
        private Integer pageNum = 1;
        
        @Schema(description = "页大小", example = "10")
        private Integer pageSize = 10;

        // getters and setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }
        public Integer getPageNum() { return pageNum; }
        public void setPageNum(Integer pageNum) { this.pageNum = pageNum; }
        public Integer getPageSize() { return pageSize; }
        public void setPageSize(Integer pageSize) { this.pageSize = pageSize; }
    }

    /**
     * 用户响应
     */
    @Schema(description = "用户信息响应")
    public static class UserResponse {
        
        @Schema(description = "用户ID", example = "1")
        private Long id;
        
        @Schema(description = "用户名", example = "admin")
        private String username;
        
        @Schema(description = "邮箱", example = "<EMAIL>")
        private String email;
        
        @Schema(description = "手机号", example = "13800138000")
        private String phone;
        
        @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
        private Integer status;
        
        @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
        private String createTime;

        // getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }
        public String getCreateTime() { return createTime; }
        public void setCreateTime(String createTime) { this.createTime = createTime; }
    }
}
