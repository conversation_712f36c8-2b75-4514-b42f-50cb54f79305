package com.fx.common.swagger.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger 网关聚合配置
 * 参考 yudao-cloud 实现，支持多服务文档聚合
 * 注意：需要在网关模块中添加 knife4j-gateway-spring-boot-starter 依赖
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(prefix = "knife4j.gateway", name = "enabled", havingValue = "true")
public class SwaggerGatewayConfiguration {

    /**
     * 网关配置说明
     *
     * 在网关模块中需要添加以下依赖：
     * <dependency>
     *     <groupId>com.github.xiaoymin</groupId>
     *     <artifactId>knife4j-gateway-spring-boot-starter</artifactId>
     * </dependency>
     *
     * 并在网关的 application.yml 中配置：
     * knife4j:
     *   gateway:
     *     enabled: true
     *     strategy: discover
     *     discover:
     *       enabled: true
     *       version: openapi3
     *       services:
     *         - name: "系统管理"
     *           service-name: fx-modules-system
     *           url: "/system/v3/api-docs"
     *         - name: "设备管理"
     *           service-name: fx-modules-link
     *           url: "/link/v3/api-docs"
     */
}
