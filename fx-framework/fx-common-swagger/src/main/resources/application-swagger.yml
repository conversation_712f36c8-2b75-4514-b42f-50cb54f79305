# Swagger OpenAPI 3 配置模板
# 参考 yudao-cloud 实现

# ==================== Swagger 配置 ====================
swagger:
  enabled: true
  title: "${spring.application.name} API 文档"
  description: "${spring.application.name} 模块 API 接口文档"
  version: "1.0"
  base-package: "com.fx"
  contact:
    name: "fx-links 开发团队"
    email: "<EMAIL>"
    url: "https://fx-links.com"
  license: "Apache 2.0"
  license-url: "https://www.apache.org/licenses/LICENSE-2.0"
  terms-of-service-url: ""
  host: ""

# ==================== SpringDoc 配置 ====================
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
    display-request-duration: true
    display-operation-id: true
    default-models-expand-depth: 1
    default-model-expand-depth: 1
    default-model-rendering: example
    doc-expansion: none
    filter: false
    show-extensions: false
    show-common-extensions: false

# ==================== Knife4j 配置 ====================
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-version: true
    enable-reload-cache-parameter: false
    enable-after-script: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: false
    enable-request-cache: true
    enable-host: false
    enable-host-text: ""
    enable-home-custom: true
    home-custom-path: classpath:markdown/home.md
    enable-search: true
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: "Copyright © 2023 fx-links"
    enable-dynamic-parameter: false
    enable-debug: true
    enable-open-api: false
    enable-group: true
  cors: false
  production: false
  basic:
    enable: false
    username: admin
    password: 123456

# ==================== 开发环境配置 ====================
---
spring:
  config:
    activate:
      on-profile: dev

swagger:
  enabled: true
  host: "http://localhost:8080"

knife4j:
  enable: true
  production: false

# ==================== 测试环境配置 ====================
---
spring:
  config:
    activate:
      on-profile: test

swagger:
  enabled: true
  host: "http://test.fx-links.com"

knife4j:
  enable: true
  production: false
  basic:
    enable: true
    username: test
    password: test123

# ==================== 生产环境配置 ====================
---
spring:
  config:
    activate:
      on-profile: prod

swagger:
  enabled: false  # 生产环境建议关闭

knife4j:
  enable: false
  production: true
