# 网关 Swagger 聚合配置示例
# 在网关模块中使用此配置

# 1. 首先在网关模块的 pom.xml 中添加依赖：
# <dependency>
#     <groupId>com.github.xiaoymin</groupId>
#     <artifactId>knife4j-gateway-spring-boot-starter</artifactId>
# </dependency>

# 2. 在网关的 application.yml 中添加以下配置：

knife4j:
  gateway:
    enabled: true
    strategy: discover
    discover:
      enabled: true
      version: openapi3
      excluded-services:
        - fx-auth
        - fx-gateway
      services:
        - name: "系统管理"
          service-name: fx-modules-system
          url: "/system/v3/api-docs"
          swagger-version: 3.0.3
          group-name: system
          order: 1
        - name: "设备管理"
          service-name: fx-modules-link
          url: "/link/v3/api-docs"
          swagger-version: 3.0.3
          group-name: link
          order: 2
        - name: "时序数据"
          service-name: fx-modules-tdengine
          url: "/tdengine/v3/api-docs"
          swagger-version: 3.0.3
          group-name: tdengine
          order: 3
        - name: "系统监控"
          service-name: fx-modules-monitor
          url: "/monitor/v3/api-docs"
          swagger-version: 3.0.3
          group-name: monitor
          order: 4

# SpringDoc 网关配置
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    urls:
      - name: "系统管理"
        url: "/system/v3/api-docs"
      - name: "设备管理"
        url: "/link/v3/api-docs"
      - name: "时序数据"
        url: "/tdengine/v3/api-docs"
      - name: "系统监控"
        url: "/monitor/v3/api-docs"

# 网关路由配置示例
spring:
  cloud:
    gateway:
      routes:
        # 系统管理服务
        - id: fx-modules-system
          uri: lb://fx-modules-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
        
        # 设备管理服务
        - id: fx-modules-link
          uri: lb://fx-modules-link
          predicates:
            - Path=/link/**
          filters:
            - StripPrefix=1
        
        # 时序数据服务
        - id: fx-modules-tdengine
          uri: lb://fx-modules-tdengine
          predicates:
            - Path=/tdengine/**
          filters:
            - StripPrefix=1
        
        # 系统监控服务
        - id: fx-modules-monitor
          uri: lb://fx-modules-monitor
          predicates:
            - Path=/monitor/**
          filters:
            - StripPrefix=1

# 跨域配置
spring:
  cloud:
    gateway:
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origins: "*"
            allowed-methods: "*"
            allowed-headers: "*"
            allow-credentials: true
