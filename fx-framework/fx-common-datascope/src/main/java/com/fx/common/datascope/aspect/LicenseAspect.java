package com.fx.common.datascope.aspect;

import com.fx.common.core.constant.CacheConstants;
import com.fx.common.core.utils.StringEncryptAndDecrpt;
import com.fx.common.datascope.annotation.License;
import com.fx.common.datascope.exception.LicenseException;
import com.fx.common.datascope.info.LicenseConstants;
import com.fx.common.datascope.verify.LicenseVerify;
import com.fx.common.datascope.verify.LicenseVerifyParam;
import com.fx.common.redis.service.RedisService;
import de.schlichtherle.license.LicenseContent;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-04-25-15:22
 */
@Aspect
@Order(1)
public class LicenseAspect {
    @Autowired
    private RedisService redisService;

    /**
     * AOP 需要判断共享组的判断点 @License
     */
    @Pointcut("@annotation(com.fx.common.datascope.annotation.License)")
    public void isLicensePointcut() {
    }

    /**
     * AOP点之前就开始判断
     */
    @Before("isLicensePointcut()")
    public void beforeIsLicensePointcutCheck(JoinPoint joinPoint) throws Exception {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        License license = method.getAnnotation(License.class);
        LicenseVerify licenseVerify = new LicenseVerify();
        if (!Objects.isNull(license)) {
            // 1. 判断证书是否已经安装 1.找项目根路径下是否存在文件，存在，已安装；不存在，查询数据库中是否存在，若存在，将该文件写入项目根路径，不存在提醒用户上传
            String filePath = LicenseConstants.LICENSE_PATH;
            File file = new File(filePath);
            if (!file.exists()) {
                String licenseFile = redisService.getCacheObject(CacheConstants.getLicenseCacheKey("licenseFile"));
                if (licenseFile != null) {
                    base64ToLic(licenseFile, filePath);
                }else {
                    throw new RuntimeException("请上传授权文件");
                }
            }
            // 2. 校验证书是否有效
            LicenseVerifyParam param = new LicenseVerifyParam();
            param.setSubject(LicenseConstants.SUBJECT);
            param.setPublicAlias(LicenseConstants.PUBLIC_ALIAS);
            param.setStorePass(LicenseConstants.STORE_PASS);
            param.setLicensePath(LicenseConstants.LICENSE_PATH);
            param.setPublicKeysStorePath(LicenseConstants.PUBLIC_KEYS_STORE_PATH);
            LicenseContent licenseContent = licenseVerify.verify(param);
            if (licenseContent == null) {
                throw new RuntimeException("您的证书无效，请核查服务器是否取得授权或重新申请证书");
            }
            // 3. 设置证书有效期
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String before = formatter.format(licenseContent.getNotBefore());
            String after = formatter.format(licenseContent.getNotAfter());
            String encrypt = StringEncryptAndDecrpt.encrypt(before, "," + after);
            redisService.setCacheObject(CacheConstants.getLicenseCacheKey("licenseTime"), encrypt);

        }
    }

    public void base64ToLic(String base64Str, String filePath) {
        byte[] bytes = Base64.getDecoder().decode(base64Str);
        try {
            File file = new File(filePath);
            file.createNewFile();
            OutputStream os = new FileOutputStream(file);
            os.write(bytes);
            System.out.println("The license file has been saved to the root directory of the project.");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
