package com.fx.common.datascope.config;

import com.fx.common.datascope.aspect.LicenseAspect;
import com.fx.common.datascope.handler.LicenseExceptionHandler;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 许可证配置类
 * 
 * <AUTHOR>
 */
@Configuration
public class LicenseConfig {
    
    /**
     * 注册许可证切面
     */
    @Bean
    @ConditionalOnMissingBean
    public LicenseAspect licenseAspect() {
        return new LicenseAspect();
    }
    
    /**
     * 注册许可证异常处理器
     */
    @Bean
    @ConditionalOnMissingBean
    public LicenseExceptionHandler licenseExceptionHandler() {
        return new LicenseExceptionHandler();
    }
}
