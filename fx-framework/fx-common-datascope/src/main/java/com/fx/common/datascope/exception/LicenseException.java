package com.fx.common.datascope.exception;

/**
 * 许可证异常类
 * 
 * <AUTHOR>
 */
public class LicenseException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 许可证异常状态码
     */
    private final int code;
    
    /**
     * 许可证状态码常量
     */
    public static class LicenseCode {
        /** 许可证未上传 */
        public static final int LICENSE_NOT_UPLOADED = 1001;
        /** 许可证已过期 */
        public static final int LICENSE_EXPIRED = 1002;
        /** 许可证无效 */
        public static final int LICENSE_INVALID = 1003;
        /** 许可证硬件不匹配 */
        public static final int LICENSE_HARDWARE_MISMATCH = 1004;
    }
    
    public LicenseException(int code, String message) {
        super(message);
        this.code = code;
    }
    
    public LicenseException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }
    
    public int getCode() {
        return code;
    }
    
    /**
     * 创建许可证未上传异常
     */
    public static LicenseException notUploaded() {
        return new LicenseException(LicenseCode.LICENSE_NOT_UPLOADED, "请上传授权文件");
    }
    
    /**
     * 创建许可证已过期异常
     */
    public static LicenseException expired() {
        return new LicenseException(LicenseCode.LICENSE_EXPIRED, "授权已过期，请重新申请授权文件");
    }
    
    /**
     * 创建许可证无效异常
     */
    public static LicenseException invalid() {
        return new LicenseException(LicenseCode.LICENSE_INVALID, "授权文件无效，请重新申请授权文件");
    }
    
    /**
     * 创建许可证硬件不匹配异常
     */
    public static LicenseException hardwareMismatch() {
        return new LicenseException(LicenseCode.LICENSE_HARDWARE_MISMATCH, "授权文件与当前服务器不匹配，请重新申请授权文件");
    }
}
