package com.fx.common.datascope.handler;

import com.fx.common.core.domain.R;
import com.fx.common.datascope.exception.LicenseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 许可证异常处理器
 * 
 * <AUTHOR>
 */
@RestControllerAdvice
@Order(1)
@Slf4j
public class LicenseExceptionHandler {
    
    /**
     * 处理许可证异常
     */
    @ExceptionHandler(LicenseException.class)
    public R<?> handleLicenseException(LicenseException e) {
        log.warn("许可证异常: code={}, message={}", e.getCode(), e.getMessage());
        return R.fail(e.getCode(), e.getMessage());
    }
}
