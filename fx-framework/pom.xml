<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>fx-links</artifactId>
        <groupId>com.fx</groupId>
        <version>${revision}</version>
    </parent>
    <packaging>pom</packaging>
    <modules>
        <module>fx-common-core</module>
        <module>fx-common-datascope</module>
        <module>fx-common-datasource</module>
        <module>fx-common-iot</module>
        <module>fx-common-job</module>
        <module>fx-common-log</module>
        <module>fx-common-redis</module>
        <module>fx-common-rocketmq</module>
        <module>fx-common-security</module>
        <module>fx-common-swagger</module>
    </modules>

    <artifactId>fx-framework</artifactId>
    <name>fx-framework</name>
    <description>
        fx-framework 技术组件的封装，每个 Maven Module 都是一个组件
    </description>

</project> 