# Swagger 升级验证脚本
# 检查升级后的配置和注解是否正确

param(
    [string]$ProjectPath = ".",
    [string]$BaseUrl = "http://localhost:8080",
    [switch]$Verbose = $false
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "  Swagger 升级验证工具" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# 验证结果统计
$validationResults = @{
    "依赖检查" = $false
    "配置文件检查" = $false
    "注解迁移检查" = $false
    "编译检查" = $false
    "文档生成检查" = $false
}

function Write-Log {
    param([string]$Message, [string]$Color = "White")
    if ($Verbose) {
        Write-Host $Message -ForegroundColor $Color
    }
}

function Test-Dependency {
    Write-Host "1. 检查依赖配置..." -ForegroundColor Yellow
    
    $pomFile = Join-Path $ProjectPath "fx-dependencies/pom.xml"
    if (-not (Test-Path $pomFile)) {
        Write-Host "  ✗ 未找到依赖管理文件: $pomFile" -ForegroundColor Red
        return $false
    }
    
    $pomContent = Get-Content $pomFile -Raw
    
    # 检查 SpringDoc 依赖
    if ($pomContent -match "springdoc-openapi-ui") {
        Write-Host "  ✓ SpringDoc 依赖配置正确" -ForegroundColor Green
    } else {
        Write-Host "  ✗ 缺少 SpringDoc 依赖" -ForegroundColor Red
        return $false
    }
    
    # 检查 Knife4j OpenAPI 3 依赖
    if ($pomContent -match "knife4j-openapi3-spring-boot-starter") {
        Write-Host "  ✓ Knife4j OpenAPI 3 依赖配置正确" -ForegroundColor Green
    } else {
        Write-Host "  ✗ 缺少 Knife4j OpenAPI 3 依赖" -ForegroundColor Red
        return $false
    }
    
    # 检查是否还有旧的 Swagger 2 依赖
    if ($pomContent -match "springfox|knife4j-openapi2") {
        Write-Host "  ⚠ 发现旧的 Swagger 2 依赖，建议清理" -ForegroundColor Yellow
    }
    
    return $true
}

function Test-Configuration {
    Write-Host "2. 检查配置文件..." -ForegroundColor Yellow
    
    $configFound = $false
    
    # 检查 Swagger 配置类
    $swaggerConfigFile = Join-Path $ProjectPath "fx-framework/fx-common-swagger/src/main/java/com/fx/common/swagger/config/SwaggerAutoConfiguration.java"
    if (Test-Path $swaggerConfigFile) {
        $configContent = Get-Content $swaggerConfigFile -Raw
        if ($configContent -match "OpenAPI|GroupedOpenApi") {
            Write-Host "  ✓ Swagger 配置类正确" -ForegroundColor Green
            $configFound = $true
        }
    }
    
    # 检查配置文件模板
    $configTemplateFile = Join-Path $ProjectPath "fx-framework/fx-common-swagger/src/main/resources/application-swagger.yml"
    if (Test-Path $configTemplateFile) {
        Write-Host "  ✓ 配置文件模板存在" -ForegroundColor Green
        $configFound = $true
    }
    
    if (-not $configFound) {
        Write-Host "  ✗ 未找到有效的配置文件" -ForegroundColor Red
        return $false
    }
    
    return $true
}

function Test-AnnotationMigration {
    Write-Host "3. 检查注解迁移..." -ForegroundColor Yellow
    
    $javaFiles = Get-ChildItem -Path $ProjectPath -Include "*.java" -Recurse | Where-Object { 
        $_.FullName -notmatch "\\target\\" -and 
        $_.FullName -notmatch "\\.git\\"
    }
    
    $oldAnnotationCount = 0
    $newAnnotationCount = 0
    $filesWithOldAnnotations = @()
    
    foreach ($file in $javaFiles) {
        $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
        if ($content) {
            # 检查旧注解
            if ($content -match "@Api\(|@ApiOperation\(|@ApiModel\(|@ApiModelProperty\(|import io\.swagger\.annotations\.") {
                $oldAnnotationCount++
                $filesWithOldAnnotations += $file.FullName
            }
            
            # 检查新注解
            if ($content -match "@Tag\(|@Operation\(|@Schema\(|import io\.swagger\.v3\.oas\.annotations\.") {
                $newAnnotationCount++
            }
        }
    }
    
    Write-Host "  发现 $newAnnotationCount 个文件使用新注解" -ForegroundColor Green
    
    if ($oldAnnotationCount -gt 0) {
        Write-Host "  ⚠ 发现 $oldAnnotationCount 个文件仍使用旧注解" -ForegroundColor Yellow
        if ($Verbose) {
            Write-Host "  需要迁移的文件:" -ForegroundColor Yellow
            $filesWithOldAnnotations | ForEach-Object { Write-Host "    $_" -ForegroundColor Gray }
        }
        return $false
    } else {
        Write-Host "  ✓ 注解迁移完成" -ForegroundColor Green
        return $true
    }
}

function Test-Compilation {
    Write-Host "4. 检查编译..." -ForegroundColor Yellow
    
    try {
        $compileResult = & mvn -f $ProjectPath clean compile -q 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✓ 编译成功" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  ✗ 编译失败" -ForegroundColor Red
            if ($Verbose) {
                Write-Host "编译错误信息:" -ForegroundColor Red
                Write-Host $compileResult -ForegroundColor Gray
            }
            return $false
        }
    } catch {
        Write-Host "  ✗ 编译检查失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-DocumentGeneration {
    Write-Host "5. 检查文档生成..." -ForegroundColor Yellow
    
    # 这里可以添加启动服务并检查文档端点的逻辑
    # 由于需要启动服务，这里只做基础检查
    
    $endpoints = @(
        "/v3/api-docs",
        "/swagger-ui/index.html",
        "/doc.html"
    )
    
    Write-Host "  建议手动验证以下端点:" -ForegroundColor Cyan
    foreach ($endpoint in $endpoints) {
        Write-Host "    $BaseUrl$endpoint" -ForegroundColor Gray
    }
    
    # 简单返回 true，实际项目中可以添加更复杂的检查逻辑
    return $true
}

function Show-ValidationSummary {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "验证结果汇总:" -ForegroundColor Green
    
    $passedCount = 0
    $totalCount = $validationResults.Count
    
    foreach ($item in $validationResults.GetEnumerator()) {
        $status = if ($item.Value) { "✓ 通过"; $passedCount++ } else { "✗ 失败" }
        $color = if ($item.Value) { "Green" } else { "Red" }
        Write-Host "  $($item.Key): $status" -ForegroundColor $color
    }
    
    Write-Host ""
    Write-Host "总体评分: $passedCount/$totalCount" -ForegroundColor $(if ($passedCount -eq $totalCount) { "Green" } else { "Yellow" })
    
    if ($passedCount -eq $totalCount) {
        Write-Host "🎉 恭喜！Swagger 升级验证全部通过！" -ForegroundColor Green
    } else {
        Write-Host "⚠ 还有部分项目需要处理，请查看上述详细信息" -ForegroundColor Yellow
    }
}

function Show-NextSteps {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "下一步建议:" -ForegroundColor Cyan
    Write-Host "1. 启动服务并访问文档页面验证" -ForegroundColor White
    Write-Host "2. 测试 API 接口调用功能" -ForegroundColor White
    Write-Host "3. 检查网关聚合配置（如果使用）" -ForegroundColor White
    Write-Host "4. 更新团队开发文档" -ForegroundColor White
    Write-Host "5. 在测试环境进行完整验证" -ForegroundColor White
    Write-Host ""
    Write-Host "文档访问地址:" -ForegroundColor Cyan
    Write-Host "  Knife4j: $BaseUrl/doc.html" -ForegroundColor Gray
    Write-Host "  Swagger UI: $BaseUrl/swagger-ui/index.html" -ForegroundColor Gray
    Write-Host "  OpenAPI JSON: $BaseUrl/v3/api-docs" -ForegroundColor Gray
}

# 执行验证
try {
    $validationResults["依赖检查"] = Test-Dependency
    $validationResults["配置文件检查"] = Test-Configuration
    $validationResults["注解迁移检查"] = Test-AnnotationMigration
    $validationResults["编译检查"] = Test-Compilation
    $validationResults["文档生成检查"] = Test-DocumentGeneration
    
    Show-ValidationSummary
    Show-NextSteps
    
} catch {
    Write-Host "验证过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "========================================" -ForegroundColor Green
