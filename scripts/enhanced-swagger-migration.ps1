# 增强版 Swagger 注解迁移脚本
# 参考 yudao-cloud 实现，支持完整的注解迁移和验证

param(
    [string]$Path = ".",
    [switch]$DryRun = $false,
    [switch]$Backup = $true,
    [switch]$Verbose = $false
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "  Swagger 2 -> OpenAPI 3 注解迁移工具" -ForegroundColor Green
Write-Host "  参考 yudao-cloud 实现" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# 统计信息
$totalFiles = 0
$updatedFiles = 0
$errorFiles = 0
$backupDir = ""

# 创建备份目录
if ($Backup -and -not $DryRun) {
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupDir = "backup_swagger_migration_$timestamp"
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
    Write-Host "备份目录: $backupDir" -ForegroundColor Yellow
}

# 注解映射配置
$annotationMappings = @{
    # 导入语句映射
    "import io.swagger.annotations.Api;" = "import io.swagger.v3.oas.annotations.tags.Tag;"
    "import io.swagger.annotations.ApiOperation;" = "import io.swagger.v3.oas.annotations.Operation;"
    "import io.swagger.annotations.ApiModel;" = "import io.swagger.v3.oas.annotations.media.Schema;"
    "import io.swagger.annotations.ApiModelProperty;" = "import io.swagger.v3.oas.annotations.media.Schema;"
    "import io.swagger.annotations.ApiImplicitParam;" = "import io.swagger.v3.oas.annotations.Parameter;"
    "import io.swagger.annotations.ApiImplicitParams;" = "import io.swagger.v3.oas.annotations.Parameters;"
    "import io.swagger.annotations.ApiParam;" = "import io.swagger.v3.oas.annotations.Parameter;"
    "import io.swagger.annotations.ApiResponse;" = "import io.swagger.v3.oas.annotations.responses.ApiResponse;"
    "import io.swagger.annotations.ApiResponses;" = "import io.swagger.v3.oas.annotations.responses.ApiResponses;"
}

# 注解使用映射
$usageMappings = @{
    # 基础注解替换
    "@Api\(" = "@Tag("
    "@ApiOperation\(" = "@Operation("
    "@ApiModel\(" = "@Schema("
    "@ApiModelProperty\(" = "@Schema("
    "@ApiImplicitParam\(" = "@Parameter("
    "@ApiParam\(" = "@Parameter("
}

# 属性名映射
$propertyMappings = @{
    "value\s*=" = "summary ="
    "notes\s*=" = "description ="
    "tags\s*=" = "name ="
}

function Write-Log {
    param([string]$Message, [string]$Color = "White")
    if ($Verbose) {
        Write-Host $Message -ForegroundColor $Color
    }
}

function Backup-File {
    param([string]$FilePath)
    if ($Backup -and -not $DryRun) {
        $relativePath = Resolve-Path $FilePath -Relative
        $backupPath = Join-Path $backupDir $relativePath
        $backupParent = Split-Path $backupPath -Parent
        if (-not (Test-Path $backupParent)) {
            New-Item -ItemType Directory -Path $backupParent -Force | Out-Null
        }
        Copy-Item $FilePath $backupPath -Force
        Write-Log "备份文件: $backupPath" "Gray"
    }
}

function Process-File {
    param([string]$FilePath)
    
    try {
        $content = Get-Content $FilePath -Raw -Encoding UTF8
        if (-not $content) {
            return $false
        }

        $originalContent = $content
        $hasSwaggerAnnotations = $false

        # 检查是否包含 Swagger 注解
        foreach ($pattern in $annotationMappings.Keys) {
            if ($content -match [regex]::Escape($pattern)) {
                $hasSwaggerAnnotations = $true
                break
            }
        }

        foreach ($pattern in $usageMappings.Keys) {
            if ($content -match $pattern) {
                $hasSwaggerAnnotations = $true
                break
            }
        }

        if (-not $hasSwaggerAnnotations) {
            return $false
        }

        Write-Host "处理文件: $FilePath" -ForegroundColor Cyan

        # 备份原文件
        Backup-File $FilePath

        # 替换导入语句
        foreach ($old in $annotationMappings.Keys) {
            $new = $annotationMappings[$old]
            $content = $content -replace [regex]::Escape($old), $new
            Write-Log "  替换导入: $old -> $new" "Gray"
        }

        # 替换注解使用
        foreach ($old in $usageMappings.Keys) {
            $new = $usageMappings[$old]
            $content = $content -replace $old, $new
            Write-Log "  替换注解: $old -> $new" "Gray"
        }

        # 替换属性名
        foreach ($old in $propertyMappings.Keys) {
            $new = $propertyMappings[$old]
            $content = $content -replace $old, $new
            Write-Log "  替换属性: $old -> $new" "Gray"
        }

        # 特殊处理复杂的注解转换
        $content = Process-ComplexAnnotations $content

        # 检查是否有变化
        if ($content -ne $originalContent) {
            if (-not $DryRun) {
                Set-Content $FilePath $content -Encoding UTF8
            }
            Write-Host "  ✓ 已更新" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  - 无变化" -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Host "  ✗ 处理失败: $($_.Exception.Message)" -ForegroundColor Red
        $script:errorFiles++
        return $false
    }
}

function Process-ComplexAnnotations {
    param([string]$Content)
    
    # 处理 @ApiImplicitParams 多参数注解
    $Content = $Content -replace '@ApiImplicitParams\s*\(\s*\{', '@Parameters({'
    $Content = $Content -replace '@ApiImplicitParams\s*\(', '@Parameters('
    
    # 处理 @ApiModelProperty 的复杂属性
    $Content = $Content -replace '@Schema\s*\(\s*value\s*=\s*"([^"]*)"', '@Schema(description = "$1"'
    $Content = $Content -replace '@Schema\s*\(\s*name\s*=\s*"([^"]*)"', '@Schema(name = "$1"'
    
    # 处理 @ApiOperation 的复杂属性
    $Content = $Content -replace '@Operation\s*\(\s*value\s*=\s*"([^"]*)"', '@Operation(summary = "$1"'
    $Content = $Content -replace '@Operation\s*\(\s*notes\s*=\s*"([^"]*)"', '@Operation(description = "$1"'
    
    return $Content
}

# 获取所有 Java 文件
Write-Host "扫描 Java 文件..." -ForegroundColor Yellow
$javaFiles = Get-ChildItem -Path $Path -Include "*.java" -Recurse | Where-Object { 
    $_.FullName -notmatch "\\target\\" -and 
    $_.FullName -notmatch "\\.git\\" -and
    $_.FullName -notmatch "\\node_modules\\" -and
    $_.FullName -notmatch "\\backup_"
}

Write-Host "找到 $($javaFiles.Count) 个 Java 文件" -ForegroundColor Yellow

if ($DryRun) {
    Write-Host "*** 试运行模式 - 不会修改文件 ***" -ForegroundColor Magenta
}

# 处理文件
foreach ($file in $javaFiles) {
    $totalFiles++
    if (Process-File $file.FullName) {
        $updatedFiles++
    }
}

# 输出统计信息
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "迁移完成统计:" -ForegroundColor Green
Write-Host "  总文件数: $totalFiles" -ForegroundColor White
Write-Host "  更新文件数: $updatedFiles" -ForegroundColor Green
Write-Host "  错误文件数: $errorFiles" -ForegroundColor Red
if ($Backup -and -not $DryRun) {
    Write-Host "  备份目录: $backupDir" -ForegroundColor Yellow
}
Write-Host "========================================" -ForegroundColor Green

# 生成迁移报告
if (-not $DryRun) {
    $reportPath = "swagger_migration_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
    @"
Swagger 2 -> OpenAPI 3 迁移报告
生成时间: $(Get-Date)
处理路径: $Path

统计信息:
- 总文件数: $totalFiles
- 更新文件数: $updatedFiles  
- 错误文件数: $errorFiles

备份目录: $backupDir

注意事项:
1. 请检查更新后的文件，确保注解转换正确
2. 运行项目测试，验证 API 文档生成正常
3. 如有问题，可从备份目录恢复文件
"@ | Out-File $reportPath -Encoding UTF8

    Write-Host "迁移报告已生成: $reportPath" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "下一步操作建议:" -ForegroundColor Yellow
Write-Host "1. 编译项目: mvn clean compile" -ForegroundColor White
Write-Host "2. 启动服务并访问: http://localhost:port/doc.html" -ForegroundColor White
Write-Host "3. 验证 API 文档是否正常显示" -ForegroundColor White
