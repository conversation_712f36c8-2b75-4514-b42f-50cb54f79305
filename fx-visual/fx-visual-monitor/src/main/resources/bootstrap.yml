# Tomcat
server:
  port: 19400

# Spring
spring: 
  application:
    # 应用名称
    name: fx-monitor
  profiles:
    # 环境配置
    active: dev
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
---
#　开发环境
spring:
  cloud:
    nacos:
      username: nacos
      password: N68DYG2JtSwH7knh
      discovery:
        # 服务注册地址
        server-addr: 192.168.9.183:8848
        namespace: e5a60688-aa9b-43fd-a9ce-efa846147949 # 通用
        #        namespace: c6556a3c-29f3-4f69-b1c3-f4950a87e240 # 大同晋控
      config:
        # 配置中心地址
        server-addr: 192.168.9.183:8848
        namespace: e5a60688-aa9b-43fd-a9ce-efa846147949 # 通用
        #        namespace: c6556a3c-29f3-4f69-b1c3-f4950a87e240 # 大同晋控
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  config:
    activate:
      on-profile: dev
---
# 测试环境
spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 10.1.1.107:8848
        namespace: 63cebd06-0b1c-4863-ad5d-4319291c3c8d
      config:
        # 配置中心地址
        server-addr: 10.1.1.107:8848
        namespace: 63cebd06-0b1c-4863-ad5d-4319291c3c8d
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  config:
    activate:
      on-profile: test
