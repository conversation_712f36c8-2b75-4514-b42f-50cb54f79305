### 获取服务器硬件信息
GET http://localhost:8066/license/getServerInfos

### 生成许可文件-安浩浩笔记本
GET http://localhost:8066/license/generateLicense
Accept: */*
Connection: keep-alive
Content-Type: application/json

{
  "subject": "iot",
  "privateAlias": "privateKeys",
  "keyPass": "12345678A",
  "storePass": "12345678A",
  "licensePath": "",
  "privateKeysStorePath": "/privateKeys.store",
  "expiryTime": "2030-01-01 08:30:00",
  "description": "系统软件许可证书",
  "licenseCheck": {
    "ipAddress": [
      "*************",
      "***********"
    ],
    "macAddress": [
      "F8-E4-3B-A0-50-87",
      "4C-32-75-99-5C-2D",
      "E0-BE-03-55-06-47"
    ],
    "cpuSerial": null,
    "mainBoardSerial": null,
    "macCheck": false,
    "ipCheck": false,
    "cpuCheck": false,
    "boardCheck": false
  }
}

### 生成许可文件-测试环境
GET http://localhost:8066/license/generateLicense
Accept: */*
Connection: keep-alive
Content-Type: application/json

{
  "subject": "iot",
  "privateAlias": "privateKeys",
  "keyPass": "12345678A",
  "storePass": "12345678A",
  "licensePath": "",
  "privateKeysStorePath": "/privateKeys.store",
  "expiryTime": "2030-01-01 08:30:00",
  "description": "系统软件许可证书",
  "licenseCheck": {
    "ipAddress": [
      "*********"
    ],
    "macAddress": [
      "EB92D80D-3E8E-44D8-9D5E-8C5962BA1F3B"
    ],
    "cpuSerial": null,
    "mainBoardSerial": null,
    "macCheck": false,
    "ipCheck": false,
    "cpuCheck": false,
    "boardCheck": false
  }
}

### 生成许可文件-宏源
GET http://localhost:8066/license/generateLicense
Accept: */*
Connection: keep-alive
Content-Type: application/json

{
  "subject": "iot",
  "privateAlias": "privateKeys",
  "keyPass": "12345678A",
  "storePass": "12345678A",
  "licensePath": "",
  "privateKeysStorePath": "/privateKeys.store",
  "expiryTime": "2030-01-01 08:30:00",
  "description": "系统软件许可证书",
  "licenseCheck": {
    "ipAddress": [
      "*********"
    ],
    "macAddress": [
      "3170024E-47C6-4AF2-8FFF-EBDDD368CADE"
    ],
    "cpuSerial": null,
    "mainBoardSerial": null,
    "macCheck": false,
    "ipCheck": false,
    "cpuCheck": false,
    "boardCheck": false
  }
}

### 生成许可文件-华德
GET http://localhost:8066/license/generateLicense
Accept: */*
Connection: keep-alive
Content-Type: application/json

{
  "subject": "iot",
  "privateAlias": "privateKeys",
  "keyPass": "12345678A",
  "storePass": "12345678A",
  "licensePath": "",
  "privateKeysStorePath": "/privateKeys.store",
  "expiryTime": "2030-01-01 08:30:00",
  "description": "系统软件许可证书",
  "licenseCheck": {
    "ipAddress": [
      "*********"
    ],
    "macAddress": [
      "44C4C29C-3C90-0296-0010-D21D040FC524"
    ],
    "cpuSerial": null,
    "mainBoardSerial": null,
    "macCheck": false,
    "ipCheck": false,
    "cpuCheck": false,
    "boardCheck": false
  }
}


### 生成许可文件-湖北测试 dda55d57-3f59-c71d-35c6-58112296911d
GET http://localhost:8066/license/generateLicense
Accept: */*
Connection: keep-alive
Content-Type: application/json

{
  "subject": "iot",
  "privateAlias": "privateKeys",
  "keyPass": "12345678A",
  "storePass": "12345678A",
  "licensePath": "",
  "privateKeysStorePath": "/privateKeys.store",
  "expiryTime": "2030-01-01 08:30:00",
  "description": "系统软件许可证书",
  "licenseCheck": {
    "ipAddress": [
      "*********"
    ],
    "macAddress": [
      "dda55d57-3f59-c71d-35c6-58112296911d"
    ],
    "cpuSerial": null,
    "mainBoardSerial": null,
    "macCheck": false,
    "ipCheck": false,
    "cpuCheck": false,
    "boardCheck": false
  }
}

### 生成许可文件-湖北生产 a65b44ca-b04f-9847-ef11-bc02a458f440
GET http://localhost:8066/license/generateLicense
Accept: */*
Connection: keep-alive
Content-Type: application/json

{
  "subject": "iot",
  "privateAlias": "privateKeys",
  "keyPass": "12345678A",
  "storePass": "12345678A",
  "licensePath": "",
  "privateKeysStorePath": "/privateKeys.store",
  "expiryTime": "2030-01-01 08:30:00",
  "description": "系统软件许可证书",
  "licenseCheck": {
    "ipAddress": [
      "*********"
    ],
    "macAddress": [
      "a65b44ca-b04f-9847-ef11-bc02a458f440"
    ],
    "cpuSerial": null,
    "mainBoardSerial": null,
    "macCheck": false,
    "ipCheck": false,
    "cpuCheck": false,
    "boardCheck": false
  }
}

### 生成许可文件-湖北生产 a65b44ca-b04f-9847-ef11-bc02a458f440
GET http://localhost:8066/license/generateLicense
Accept: */*
Connection: keep-alive
Content-Type: application/json

{
  "subject": "iot",
  "privateAlias": "privateKeys",
  "keyPass": "12345678A",
  "storePass": "12345678A",
  "licensePath": "",
  "privateKeysStorePath": "/privateKeys.store",
  "expiryTime": "2025-12-01 08:30:00",
  "description": "系统软件许可证书",
  "licenseCheck": {
    "ipAddress": [
      "**************"
    ],
    "macAddress": [
      "E1C5D866-0018-8320-B211-D21D52621524"
    ],
    "cpuSerial": null,
    "mainBoardSerial": null,
    "macCheck": false,
    "ipCheck": false,
    "cpuCheck": false,
    "boardCheck": false
  }
}
### 生成许可文件-湖北生产1 f49ecb6a-a851-4084-8932-ca0c1fd21c69
GET http://localhost:8066/license/generateLicense
Accept: */*
Connection: keep-alive
Content-Type: application/json

{
  "subject": "iot",
  "privateAlias": "privateKeys",
  "keyPass": "12345678A",
  "storePass": "12345678A",
  "licensePath": "",
  "privateKeysStorePath": "/privateKeys.store",
  "expiryTime": "2030-01-01 00:00:00",
  "description": "系统软件许可证书",
  "licenseCheck": {
    "ipAddress": [
      "*************"
    ],
    "macAddress": [
      "f49ecb6a-a851-4084-8932-ca0c1fd21c69"
    ],
    "cpuSerial": null,
    "mainBoardSerial": null,
    "macCheck": false,
    "ipCheck": false,
    "cpuCheck": false,
    "boardCheck": false
  }
}

### 生成许可文件-生产 8A228327-1D0C-44F0-9E8D-6FA775896C1A
GET http://localhost:8066/license/generateLicense
Accept: */*
Connection: keep-alive
Content-Type: application/json

{
  "subject": "iot",
  "privateAlias": "privateKeys",
  "keyPass": "12345678A",
  "storePass": "12345678A",
  "licensePath": "",
  "privateKeysStorePath": "/privateKeys.store",
  "expiryTime": "2030-01-01 00:00:00",
  "description": "系统软件许可证书",
  "licenseCheck": {
    "ipAddress": [
      "***********"
    ],
    "macAddress": [
      "8A228327-1D0C-44F0-9E8D-6FA775896C1A"
    ],
    "cpuSerial": null,
    "mainBoardSerial": null,
    "macCheck": false,
    "ipCheck": false,
    "cpuCheck": false,
    "boardCheck": false
  }
}