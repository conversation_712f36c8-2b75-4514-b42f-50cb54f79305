<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.fx</groupId>
    <artifactId>fx-dependencies</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    
    <name>fx-dependencies</name>
    <description>基础 BOM 文件，管理整个项目的依赖版本</description>

    <properties>
        <revision>2.0.0-SNAPSHOT</revision>

        <!-- 编码相关 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>

        <!-- Java 版本 -->
        <java.version>1.8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        
        <!-- Spring 版本管理 -->
        <spring-boot.version>2.6.6</spring-boot.version>
        <spring-cloud.version>2021.0.2</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.1</spring-cloud-alibaba.version>
        <spring-boot-gateway.version>3.0.7</spring-boot-gateway.version>
        <alibaba.nacos.version>2.0.3</alibaba.nacos.version>
        <spring-boot-admin.version>2.6.7</spring-boot-admin.version>
        <spring-boot.mybatis>2.2.2</spring-boot.mybatis>

        <!-- 接口文档 -->
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <swagger.ui.version>2.9.2</swagger.ui.version>
        <swagger.knife4j.version>3.0.3</swagger.knife4j.version>
        <swagger.core.version>1.6.2</swagger.core.version>

        <!-- 数据库驱动 -->
        <mysql.version>8.0.28</mysql.version>
        <dameng.version>8.1.3.140</dameng.version>
        <tdengine.version>3.2.11</tdengine.version>
        <postgresql.version>42.3.3</postgresql.version>
        <oracle.version>21.5.0.0</oracle.version>
        <mssql.version>10.2.0.jre8</mssql.version>

        <!-- 数据库连接池 -->
        <druid.version>1.2.15</druid.version>
        <dynamic-ds.version>3.5.2</dynamic-ds.version>
        
        <!-- MyBatis 相关 -->
        <mybatis-plus.version>3.5.2</mybatis-plus.version>
        <mybatis-plus-generator.version>3.5.2</mybatis-plus-generator.version>
        <dynamic-datasource.version>3.5.2</dynamic-datasource.version>

        <!-- Redis 相关 -->
        <redisson.version>3.18.1</redisson.version>

        <!-- 分页插件 -->
        <pagehelper.boot.version>1.4.1</pagehelper.boot.version>

        <!-- 工具类库 -->
        <hutool.version>5.8.25</hutool.version>
        <guava.version>31.1-jre</guava.version>
        <gson.version>2.10.1</gson.version>
        <fastjson.version>1.2.83</fastjson.version>
        <fastjson2.version>2.0.25</fastjson2.version>
        <jackson.version>2.13.5</jackson.version>
        <json-path.version>2.7.0</json-path.version>

        <!-- Apache Commons 工具类 -->
        <commons-io.version>2.11.0</commons-io.version>
        <commons-fileupload.version>1.4</commons-fileupload.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <commons-text.version>1.10.0</commons-text.version>
        <commons-pool2.version>2.11.1</commons-pool2.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <commons-codec.version>1.15</commons-codec.version>

        <!-- 网络与通信 -->
        <okhttp3.version>4.10.0</okhttp3.version>
        <mqtt.version>1.2.5</mqtt.version>
        <openfeign.version>3.1.2</openfeign.version>
        <netty.version>4.1.77.Final</netty.version>
        
        <!-- Vert.x -->
        <vertx.version>4.2.2</vertx.version>

        <!-- 文件存储 -->
        <minio.version>8.4.6</minio.version>

        <!-- 安全框架 -->
        <spring-security.version>5.7.5.RELEASE</spring-security.version>
        <spring-security-oauth2.version>2.5.2.RELEASE</spring-security-oauth2.version>

        <!-- 定时任务 -->
        <quartz.version>2.3.2</quartz.version>
        <xxl-job.version>2.3.1</xxl-job.version>

        <!-- Excel 操作 -->
        <poi.version>5.2.2</poi.version>
        <easyexcel.version>3.1.1</easyexcel.version>

        <!-- 模板引擎 -->
        <velocity.version>2.3</velocity.version>
        <freemarker.version>2.3.31</freemarker.version>

        <!-- 验证码 -->
        <kaptcha.version>2.3.2</kaptcha.version>
        <easy-captcha.version>1.6.2</easy-captcha.version>

        <!-- 对象映射 -->
        <mapstruct.version>1.5.3.Final</mapstruct.version>

        <!-- 监控相关 -->
        <micrometer.version>1.9.0</micrometer.version>
        <spring-boot-admin.version>2.6.7</spring-boot-admin.version>

        <!-- 测试框架 -->
        <mockito.version>4.6.1</mockito.version>
        <junit.version>5.8.2</junit.version>

        <!-- 消息队列 -->
        <rocketmq-spring.version>2.3.1</rocketmq-spring.version>

        <!-- 其他第三方库 -->
        <tobato.version>1.27.2</tobato.version>
        <luaj.jse.version>3.0.1</luaj.jse.version>
        <truelicense-core.version>1.33</truelicense-core.version>
        <oshi-core.version>5.6.1</oshi-core.version>

        <!-- 插件版本 -->
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
        <maven-resources-plugin.version>3.2.0</maven-resources-plugin.version>
        <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
        <maven-javadoc-plugin.version>3.4.0</maven-javadoc-plugin.version>
        <maven-surefire-plugin.version>3.0.0-M7</maven-surefire-plugin.version>
        
        <!-- Lombok 版本 -->
        <lombok.version>1.18.20</lombok.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            
            <!-- ==================== Spring Boot 相关 ==================== -->
            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- ==================== Spring Cloud 相关 ==================== -->
            <!-- Spring Cloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- ==================== 数据库相关 ==================== -->
            <!-- MySQL 驱动 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <!-- 达梦数据库 -->
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dameng.version}</version>
            </dependency>

            <!-- TDengine 驱动 -->
            <dependency>
                <groupId>com.taosdata.jdbc</groupId>
                <artifactId>taos-jdbcdriver</artifactId>
                <version>${tdengine.version}</version>
            </dependency>

            <!-- PostgreSQL 驱动 -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>

            <!-- Oracle 驱动 -->
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${oracle.version}</version>
            </dependency>

            <!-- SQL Server 驱动 -->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${mssql.version}</version>
            </dependency>

            <!-- Druid 数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- MyBatis Plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus-generator.version}</version>
            </dependency>

            <!-- 动态数据源 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>

            <!-- ==================== Redis 相关 ==================== -->
            <!-- Redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- ==================== 工具类库 ==================== -->
            <!-- Hutool 工具类 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- Google Guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- JSON 处理 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>

            <!-- Apache Commons -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons-fileupload.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool2.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>

            <!-- ==================== 网络与通信 ==================== -->
            <!-- OkHttp -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>

            <!-- MQTT -->
            <dependency>
                <groupId>org.eclipse.paho</groupId>
                <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
                <version>${mqtt.version}</version>
            </dependency>

            <!-- Netty -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>

            <!-- Vert.x -->
            <dependency>
                <groupId>io.vertx</groupId>
                <artifactId>vertx-web-proxy</artifactId>
                <version>${vertx.version}</version>
            </dependency>

            <!-- ==================== 文件存储 ==================== -->
            <!-- MinIO -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <!-- FastDFS -->
            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${tobato.version}</version>
            </dependency>

            <!-- ==================== Excel 处理 ==================== -->
            <!-- Apache POI -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- EasyExcel -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!-- ==================== 模板引擎 ==================== -->
            <!-- Velocity Engine Core -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- ==================== 验证码 ==================== -->
            <!-- Kaptcha -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- Easy Captcha -->
            <dependency>
                <groupId>com.github.whvcse</groupId>
                <artifactId>easy-captcha</artifactId>
                <version>${easy-captcha.version}</version>
            </dependency>

            <!-- ==================== 定时任务 ==================== -->
            <!-- Quartz -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-quartz</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <!-- XXL-JOB -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <!-- ==================== 监控相关 ==================== -->
            <!-- Spring Boot Admin -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!-- Micrometer -->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-core</artifactId>
                <version>${micrometer.version}</version>
            </dependency>

            <!-- ==================== 接口文档 ==================== -->
            <!-- Swagger -->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>

            <!-- Knife4j -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${swagger.knife4j.version}</version>
            </dependency>

            <!-- Swagger -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.fox.version}</version>
            </dependency>

            <!-- Swagger UI -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger.ui.version}</version>
            </dependency>

            <!-- SpringFox Boot Starter -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${swagger.fox.version}</version>
            </dependency>

            <!-- ==================== 对象映射 ==================== -->
            <!-- MapStruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <!-- ==================== 测试相关 ==================== -->
            <!-- Mockito -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
            </dependency>

            <!-- ==================== 分页插件 ==================== -->
            <!-- PageHelper -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- ==================== 其他第三方库 ==================== -->
            <!-- LuaJ -->
            <dependency>
                <groupId>org.luaj</groupId>
                <artifactId>luaj-jse</artifactId>
                <version>${luaj.jse.version}</version>
            </dependency>

            <!-- TrueLicense -->
            <dependency>
                <groupId>de.schlichtherle.truelicense</groupId>
                <artifactId>truelicense-core</artifactId>
                <version>${truelicense-core.version}</version>
            </dependency>

            <!-- Oshi -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi-core.version}</version>
            </dependency>

            <!-- RocketMQ -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-spring.version}</version>
            </dependency>

            <!-- ==================== 本项目模块 ==================== -->
            <!-- 核心模块 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-common-swagger</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-common-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-common-datascope</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-common-datasource</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-common-log</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- IoT 物联网 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-common-iot</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 定时任务 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-common-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 消息队列 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-common-rocketmq</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-api-system</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Broker接口 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-api-broker</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Link接口 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-api-link</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Monitor接口 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-api-monitor</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Rule接口 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-api-rule</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Job接口 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-api-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- TDengine接口 -->
            <dependency>
                <groupId>com.fx</groupId>
                <artifactId>fx-api-tdengine</artifactId>
                <version>${revision}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- maven-compiler-plugin 插件，解决 Lombok + MapStruct 组合 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-configuration-processor</artifactId>
                                <version>${spring-boot.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                
                <!-- maven-jar-plugin 插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                </plugin>
                
                <!-- maven-resources-plugin 插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                </plugin>
                
                <!-- maven-source-plugin 插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.version}</version>
                </plugin>
                
                <!-- maven-javadoc-plugin 插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven-javadoc-plugin.version}</version>
                </plugin>
                
                <!-- maven-surefire-plugin 插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <repositories>
        <repository>
            <id>central</id>
            <url>https://maven.aliyun.com/repository/central</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>public</id>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

</project>