package com.fx.auth.controller;

import com.fx.common.core.constant.CacheConstants;
import com.fx.common.core.domain.R;
import com.fx.common.datascope.info.LicenseConstants;
import com.fx.common.datascope.manager.CustomLicenseManager;
import com.fx.common.datascope.model.LicenseCheckModel;
import com.fx.common.datascope.verify.LicenseVerify;
import com.fx.common.datascope.verify.LicenseVerifyParam;
import com.fx.common.redis.service.RedisService;
import de.schlichtherle.license.LicenseContent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 授权控制器类
 */
@Api(tags = "授权")
@RestController
@Slf4j
public class LicenseController {

    @Resource
    private RedisService redisService;

    /**
     * 读取文件内容为字节数组
     *
     * @param filePath 文件路径
     * @return 文件内容字节数组
     * @throws IOException IO异常
     */
    private static byte[] readFileToBytes(String filePath) throws IOException {
        File file = new File(filePath);
        byte[] fileContent = new byte[(int) file.length()];
        try (FileInputStream fis = new FileInputStream(file)) {
            fis.read(fileContent);
        }
        return fileContent;
    }

    /**
     * 初始化方法，在Bean创建后调用
     *
     * @throws IOException IO异常
     */
    @PostConstruct
    public void init() throws IOException {
        String licenseFile = redisService.getCacheObject(CacheConstants.getLicenseCacheKey("licenseFile"));
        if (licenseFile != null) {
            installLicense();
        }
    }

    /**
     * 授权证书上传接口
     *
     * @param file 上传的文件
     * @return 响应结果
     * @throws Exception 异常
     */
    @PostMapping("/licenseUpload")
    @ApiOperation(value = "授权证书上传")
    public R<?> licenseUpload(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new Exception("授权证书不能为空");
        }
        File dest = saveFile(file);
        LicenseContent licenseContent = installLicense();

        // 构建返回数据
        Map<String, Object> data = new HashMap<>();
        data.put("info", licenseContent.getInfo());
        data.put("notBefore", licenseContent.getNotBefore());
        data.put("notAfter", licenseContent.getNotAfter());

        return R.ok(data);
    }

    /**
     * 查询MAC地址接口
     *
     * @return 响应结果
     */
    @GetMapping("findMacAddress")
    @ApiOperation(value = "查询mac地址")
    public R<?> findMacAddress() {
        CustomLicenseManager customLicenseManager = new CustomLicenseManager();
        LicenseCheckModel serverInfos = customLicenseManager.getServerInfos();
        return R.ok(serverInfos.getMacAddress().get(0));
    }

    /**
     * 安装许可证
     *
     * @return 许可证内容对象
     * @throws IOException IO异常
     */
    private LicenseContent installLicense() throws IOException {
        LicenseVerifyParam param = new LicenseVerifyParam();
        param.setSubject(LicenseConstants.SUBJECT);
        param.setPublicAlias(LicenseConstants.PUBLIC_ALIAS);
        param.setStorePass(LicenseConstants.STORE_PASS);
        param.setLicensePath(LicenseConstants.LICENSE_PATH);
        param.setPublicKeysStorePath(LicenseConstants.PUBLIC_KEYS_STORE_PATH);
        LicenseVerify licenseVerify = new LicenseVerify();
        log.info("安装证书,参数:{}", param);
        LicenseContent licenseContent = licenseVerify.install(param);
        if (licenseContent != null) {
            byte[] fileContent = readFileToBytes(LicenseConstants.LICENSE_PATH);
            String encodedString = Base64.getEncoder().encodeToString(fileContent);
            redisService.setCacheObject(CacheConstants.getLicenseCacheKey("licenseFile"), encodedString);
            return licenseContent;
        } else {
            redisService.setCacheObject(CacheConstants.getLicenseCacheKey("licenseFile"), null);
            throw new RuntimeException("证书不存在或已失效");
        }
    }

    /**
     * 保存上传的文件
     *
     * @param file 上传的文件
     * @return 保存后的文件
     * @throws IOException IO异常
     */
    private File saveFile(MultipartFile file) throws IOException {
        File dir = new File(System.getProperty("user.dir") + File.separator + "upload" + File.separator);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String suffix = "";
        String originalFilename = file.getOriginalFilename();
        int index = 0;
        if (originalFilename != null) {
            index = originalFilename.indexOf(".");
        }
        if (index > 0) {
            suffix = originalFilename.substring(index);
        }
        String fileName = "license" + suffix;
        File file1 = new File(LicenseConstants.LICENSE_PATH);
        file1.delete();
        File dest = new File(dir, fileName);
        file.transferTo(dest);
        return dest;
    }
}