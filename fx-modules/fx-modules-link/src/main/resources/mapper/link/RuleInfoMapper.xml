<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fx.link.mapper.RuleInfoMapper">

    <resultMap type="RuleInfo" id="RuleInfoResult">
        <result property="id"    column="id"    />
        <result property="filters"    column="filters"    />
        <result property="listeners"    column="listeners"    />
        <result property="actions"    column="actions"    />
        <result property="name"    column="name"    />
        <result property="status"    column="status"    />
        <result property="type"    column="type"    />
        <result property="description"    column="description"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRuleInfoVo">
        select id, filters, listeners, actions, name, status, type, description, create_by, create_time, update_by, update_time from rule_info
    </sql>

    <select id="selectRuleInfoList" parameterType="RuleInfo" resultMap="RuleInfoResult">
        <include refid="selectRuleInfoVo"/>
        <where>
            <if test="filters != null  and filters != ''"> and filters = #{filters}</if>
            <if test="listeners != null  and listeners != ''"> and listeners = #{listeners}</if>
            <if test="actions != null  and actions != ''"> and actions = #{actions}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="id != null"> and id = #{id}</if>
        </where>
    </select>

    <select id="selectRuleInfoById" parameterType="Long" resultMap="RuleInfoResult">
        <include refid="selectRuleInfoVo"/>
        where id = #{id}
    </select>
    <select id="selectRuleNameList" resultType="com.fx.link.api.domain.device.entity.RuleInfo">
        <include refid="selectRuleInfoVo"/>
        <where>
            <if test="name != null  and name != ''"> and name = #{name}</if>
            <if test="id != null"> and id != #{id}</if>
            <if test="type != null and type != ''"> and type = #{type}</if>
        </where>
    </select>
    <select id="selectSceneRuleList" parameterType="RuleInfo" resultMap="RuleInfoResult">
        select r.id, filters, r.listeners, r.actions, r.name, r.status, r.type, r.description, r.create_by, r.create_time, r.update_by, r.update_time from rule_info r
        left join  warn_info w on r.id = w.trigger_id
        <where>
            (w.trigger_id IS NULL
            <if test="id != null"> or w.trigger_id = #{id}</if>)
            <if test="type != null  and type != ''"> and r.type = #{type}</if>
        </where>
    </select>

    <insert id="insertRuleInfo" parameterType="RuleInfo" useGeneratedKeys="true" keyProperty="id">
        insert into rule_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="filters != null">filters,</if>
            <if test="listeners != null">listeners,</if>
            <if test="actions != null">actions,</if>
            <if test="name != null">name,</if>
            <if test="status != null">status,</if>
            <if test="type != null">type,</if>
            <if test="description != null">description,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="filters != null">#{filters},</if>
            <if test="listeners != null">#{listeners},</if>
            <if test="actions != null">#{actions},</if>
            <if test="name != null">#{name},</if>
            <if test="status != null">#{status},</if>
            <if test="type != null">#{type},</if>
            <if test="description != null">#{description},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRuleInfo" parameterType="RuleInfo">
        update rule_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="filters != null">filters = #{filters},</if>
            <if test="listeners != null">listeners = #{listeners},</if>
            <if test="actions != null">actions = #{actions},</if>
            <if test="name != null">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
            <if test="type != null">type = #{type},</if>
            <if test="description != null">description = #{description},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateRuleInfoStatus">
        update rule_info set status =#{status} where id =#{id}
    </update>

    <delete id="deleteRuleInfoById" parameterType="Long">
        delete from rule_info where id = #{id}
    </delete>

    <delete id="deleteRuleInfoByIds" parameterType="String">
        delete from rule_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
