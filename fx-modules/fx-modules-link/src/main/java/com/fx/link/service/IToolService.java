package com.fx.link.service;

import com.fx.link.api.domain.model.MqttAuthModel;
import com.fx.link.api.domain.model.MqttAuthResultModel;
import com.fx.link.api.domain.model.MqttClientConnectModel;

/**
 * 工具服务接口
 * 提供 MQTT 钩子处理和 MQTT 授权等方法
 * 使用 MqttClientConnectModel、MqttAuthModel 和 MqttAuthResultModel 数据模型
 * 该接口需要实现类来实现具体的业务逻辑
 *
 * <AUTHOR>
 * @since 2024/5/18 上午1:06
 */
public interface IToolService {

    /**
     * MQTT 钩子处理方法
     * 接收一个 MqttClientConnectModel 对象，进行 MQTT 钩子处理
     *
     * @param model MQTT 客户端连接模型，类型为 MqttClientConnectModel
     */
    void webHookProcess(MqttClientConnectModel model);

    /**
     * MQTT 授权方法
     * 接收一个 MqttAuthModel 对象，进行 MQTT 授权
     * 返回一个 MqttAuthResultModel 对象，表示授权结果
     *
     * @param model MQTT 授权模型，类型为 MqttAuthModel
     * @return 授权结果，类型为 MqttAuthResultModel
     */
    MqttAuthResultModel mqttAuth(MqttAuthModel model);

    /**
     * 初始化产品和设备
     */
    void initProduct();

    /**
     * 初始化设备
     */
    void initDevice();

    /**
     * 初始化缓存
     */
    void initDeviceCache();
}