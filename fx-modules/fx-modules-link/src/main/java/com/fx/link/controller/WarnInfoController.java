package com.fx.link.controller;

import com.fx.common.core.utils.poi.ExcelUtil;
import com.fx.common.core.web.controller.BaseController;
import com.fx.common.core.web.domain.AjaxResult;
import com.fx.common.core.web.page.TableDataInfo;
import com.fx.common.log.annotation.Log;
import com.fx.common.log.enums.BusinessType;
import com.fx.common.security.annotation.PreAuthorize;
import com.fx.link.api.domain.device.entity.RuleInfo;
import com.fx.link.api.domain.warn.Dto.BatchUpdateWarnStatus;
import com.fx.link.api.domain.warn.WarnInfo;
import com.fx.link.service.IWarnInfoService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 告警配置控制器
 * 提供告警配置相关的 HTTP 接口，包括告警配置的查询、新增、修改、删除、导出等操作
 * 使用 IWarnInfoService 服务处理告警配置相关的业务逻辑
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/warnInfo")
@Api(tags = "告警配置")
public class WarnInfoController extends BaseController {

    @Resource
    private IWarnInfoService warnInfoService;

    /**
     * 查询告警配置列表的接口
     * 接收一个 WarnInfo 对象，根据其属性查询匹配的告警配置列表，并返回分页的告警配置列表
     *
     * @param warnInfo 查询条件，类型为 WarnInfo
     * @return 分页的告警配置列表，类型为 TableDataInfo
     */
    @PreAuthorize(hasPermi = "link:warnInfo:list")
    @GetMapping("/list")
    public TableDataInfo list(WarnInfo warnInfo) {
        startPage();
        List<WarnInfo> list = warnInfoService.selectWarnInfoList(warnInfo);
        return getDataTable(list);
    }

    /**
     * 导出告警配置列表的接口
     * 接收一个 WarnInfo 对象，根据其属性查询匹配的告警配置列表，并将查询结果导出为 Excel 文件
     *
     * @param response HTTP 响应，用于返回生成的 Excel 文件
     * @param warnInfo 查询条件，类型为 WarnInfo
     * @throws IOException 当文件操作出现异常时抛出
     */
    @PreAuthorize(hasPermi = "link:warnInfo:export")
    @Log(title = "告警配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WarnInfo warnInfo) throws IOException {
        List<WarnInfo> list = warnInfoService.selectWarnInfoList(warnInfo);
        ExcelUtil<WarnInfo> util = new ExcelUtil<>(WarnInfo.class);
        util.exportExcel(response, list, "告警配置数据");
    }

    /**
     * 获取告警配置详细信息的接口
     * 接收一个 id，查询对应的告警配置
     *
     * @param id 告警配置的 id
     * @return 查询到的告警配置，类型为 AjaxResult
     */
    @PreAuthorize(hasPermi = "link:warnInfo:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(warnInfoService.selectWarnInfoById(id));
    }

    /**
     * 新增告警配置的接口
     * 接收一个 WarnInfo 对象，将其保存为新的告警配置
     *
     * @param warnInfo 要新增的告警配置，类型为 WarnInfo
     * @return 操作结果，类型为 AjaxResult
     */
    @PreAuthorize(hasPermi = "link:warnInfo:add")
    @Log(title = "告警配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WarnInfo warnInfo) {
        return toAjax(warnInfoService.insertWarnInfo(warnInfo));
    }

    /**
     * 修改告警配置的接口
     * 接收一个 WarnInfo 对象，更新对应的告警配置
     *
     * @param warnInfo 要更新的告警配置，类型为 WarnInfo
     * @return 操作结果，类型为 AjaxResult
     */
    @PreAuthorize(hasPermi = "link:warnInfo:edit")
    @Log(title = "告警配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WarnInfo warnInfo) {
        return toAjax(warnInfoService.updateWarnInfo(warnInfo));
    }

    /**
     * 删除告警配置的接口
     * 接收一个 id，删除对应的告警配置
     *
     * @param id 告警配置的 id
     * @return 操作结果，类型为 AjaxResult
     */
    @PreAuthorize(hasPermi = "link:warnInfo:remove")
    @Log(title = "告警配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(warnInfoService.deleteWarnInfoById(id));
    }

    /**
     * 批量修改告警配置状态的接口
     * 接收一个 BatchUpdateWarnStatus 对象，批量更新对应的告警配置状态
     *
     * @param batchUpdateWarnStatus 批量更新状态的对象，类型为 BatchUpdateWarnStatus
     * @return 操作结果，类型为 AjaxResult
     */
    @PreAuthorize(hasPermi = "link:warnInfo:updateStatus")
    @Log(title = "告警配置状态修改", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus")
    public AjaxResult updateStatus(@RequestBody BatchUpdateWarnStatus batchUpdateWarnStatus) {
        return toAjax(warnInfoService.updateWarnInfoStatusByIds(batchUpdateWarnStatus.getIds(), batchUpdateWarnStatus.getStatus()));
    }

    /**
     * 查询告警配置列表的接口
     * 接收一个 RuleInfo 对象，根据其属性查询匹配的告警配置列表，并返回查询结果
     *
     * @param ruleInfo 查询条件，类型为 RuleInfo
     * @return 查询结果，类型为 AjaxResult
     */
    //@PreAuthorize(hasPermi = "link:warnInfo:list")
    @GetMapping("/findSceneRulelist")
    public AjaxResult findSceneRulelist(RuleInfo ruleInfo) {
        return AjaxResult.success(warnInfoService.findSceneRuleList(ruleInfo));
    }
}