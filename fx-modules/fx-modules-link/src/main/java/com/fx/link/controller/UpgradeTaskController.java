package com.fx.link.controller;

import com.fx.common.core.constant.UserConstants;
import com.fx.common.core.utils.SecurityUtils;
import com.fx.common.core.web.controller.BaseController;
import com.fx.common.core.web.domain.AjaxResult;
import com.fx.common.core.web.page.TableDataInfo;
import com.fx.common.log.annotation.Log;
import com.fx.common.log.enums.BusinessType;
import com.fx.common.security.annotation.PreAuthorize;
import com.fx.link.api.domain.device.entity.UpgradeTask;
import com.fx.link.service.IUpgradeTaskService;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 升级任务控制器
 * 提供升级任务相关的 HTTP 接口，包括升级任务的查询、新增、修改、删除、执行等操作
 * 使用 IUpgradeTaskService 服务处理升级任务相关的业务逻辑
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/upgradeTask")
@Api(tags = "升级任务管理")
public class UpgradeTaskController extends BaseController {

    @Resource
    private IUpgradeTaskService upgradeTaskService;

    /**
     * 查询升级任务列表的接口
     * 接收一个 UpgradeTask 对象，根据其属性查询匹配的升级任务列表，并返回分页的升级任务列表
     *
     * @param upgradeTask 查询条件，类型为 UpgradeTask
     * @return 分页的升级任务列表，类型为 TableDataInfo
     */
    @PreAuthorize(hasPermi = "link:upgradeTask:list")
    @GetMapping("/list")
    public TableDataInfo list(UpgradeTask upgradeTask) {
        startPage();
        List<UpgradeTask> upgradeTasks = upgradeTaskService.selectUpgradeTaskList(upgradeTask);
        return getDataTable(upgradeTasks);
    }

    /**
     * 获取升级任务详细信息的接口
     * 接收一个 upgradeTaskId，查询对应的升级任务
     *
     * @param upgradeTaskId 升级任务的 id
     * @return 查询到的升级任务，类型为 AjaxResult
     */
    @PreAuthorize(hasPermi = "link:upgradeTask:getInfo")
    @GetMapping(value = "/{upgradeTaskId}")
    public AjaxResult getInfo(@PathVariable Long upgradeTaskId) {
        return AjaxResult.success(upgradeTaskService.selectUpgradeTaskById(upgradeTaskId));
    }

    /**
     * 新增升级任务的接口
     * 接收一个 UpgradeTask 对象，将其保存为新的升级任务
     *
     * @param upgradeTask 要新增的升级任务，类型为 UpgradeTask
     * @return 操作结果，类型为 AjaxResult
     */
    @PreAuthorize(hasPermi = "link:upgradeTask:add")
    @Log(title = "升级任务管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody UpgradeTask upgradeTask) {
        if (UserConstants.NOT_UNIQUE.equals(upgradeTaskService.checkUpgradeTaskUnique(upgradeTask))) {
            return AjaxResult.error("新增升级任务'" + upgradeTask.getTaskName() + "'失败，升级任务名称已存在");
        }
        upgradeTask.setCreateBy(SecurityUtils.getUsername());
        return toAjax(upgradeTaskService.insertUpgradeTask(upgradeTask));
    }

    /**
     * 修改升级任务的接口
     * 接收一个 UpgradeTask 对象，更新对应的升级任务
     *
     * @param upgradeTask 要更新的升级任务，类型为 UpgradeTask
     * @return 操作结果，类型为 AjaxResult
     */
    @PreAuthorize(hasPermi = "link:upgradeTask:edit")
    @Log(title = "升级任务管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody UpgradeTask upgradeTask) {
        if (UserConstants.NOT_UNIQUE.equals(upgradeTaskService.checkUpgradeTaskUnique(upgradeTask))) {
            return AjaxResult.error("修改升级任务'" + upgradeTask.getTaskName() + "'失败，升级任务名称已存在");
        }
        upgradeTask.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(upgradeTaskService.updateUpgradeTask(upgradeTask));
    }

    /**
     * 删除升级任务的接口
     * 接收一个 upgradeTaskId，删除对应的升级任务
     *
     * @param upgradeTaskId 升级任务的 id
     * @return 操作结果，类型为 AjaxResult
     */
    @PreAuthorize(hasPermi = "link:upgradeTask:remove")
    @Log(title = "升级任务管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{upgradeTaskId}")
    public AjaxResult remove(@PathVariable Long upgradeTaskId) {
        return toAjax(upgradeTaskService.deleteUpgradeTaskById(upgradeTaskId));
    }

    /**
     * 执行升级任务的接口
     * 接收一个 UpgradeTask 对象，执行对应的升级任务
     *
     * @param upgradeTask 要执行的升级任务，类型为 UpgradeTask
     * @return 操作结果，类型为 AjaxResult
     */
    @PreAuthorize(hasPermi = "link:upgradeTask:execute")
    @GetMapping("/execute")
    public AjaxResult execute(UpgradeTask upgradeTask) {
        int listCount = upgradeTaskService.executeUpgradeTask(upgradeTask);
        if (listCount == 0) {
            return AjaxResult.error("任务状态更新失败");
        }
        return toAjax(listCount);
    }
}