package com.fx.link.service;

import com.fx.link.api.domain.device.entity.Topic;

import java.util.List;

/**
 * 设备Topic数据Service接口
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
public interface ITopicService {
    /**
     * 查询设备Topic数据
     *
     * @param id 设备Topic数据主键
     * @return 设备Topic数据
     */
    public Topic selectTopicById(Long id);

    /**
     * 查询设备Topic数据列表
     *
     * @param topic 设备Topic数据
     * @return 设备Topic数据集合
     */
    public List<Topic> selectTopicList(Topic topic);

    /**
     * 新增设备Topic数据
     *
     * @param topic 设备Topic数据
     * @return 结果
     */
    public int insertTopic(Topic topic);

    /**
     * 修改设备Topic数据
     *
     * @param topic 设备Topic数据
     * @return 结果
     */
    public int updateTopic(Topic topic);

    /**
     * 批量删除设备Topic数据
     *
     * @param ids 需要删除的设备Topic数据主键集合
     * @return 结果
     */
    public int deleteTopicByIds(Long[] ids);

    /**
     * 删除设备Topic数据信息
     *
     * @param id 设备Topic数据主键
     * @return 结果
     */
    public int deleteTopicById(Long id);
}
