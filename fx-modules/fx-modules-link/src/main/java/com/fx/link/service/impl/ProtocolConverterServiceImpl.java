package com.fx.link.service.impl;

import com.fx.common.core.constant.CacheConstants;
import com.fx.common.core.exception.ServiceException;
import com.fx.common.core.utils.DateUtils;
import com.fx.common.redis.service.RedisService;
import com.fx.link.api.domain.protocol.entity.ProtocolConverter;
import com.fx.link.mapper.ProtocolConverterMapper;
import com.fx.link.service.IProtocolConverterService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 协议转换Service业务层处理
 */
@Service
public class ProtocolConverterServiceImpl implements IProtocolConverterService {

    @Resource
    private ProtocolConverterMapper protocolConverterMapper;
    @Resource
    private RedisService redisService;

    /**
     * 查询协议转换
     */
    @Override
    public ProtocolConverter selectProtocolConverterById(Long id) {
        return protocolConverterMapper.selectProtocolConverterById(id);
    }

    /**
     * 查询协议转换列表
     */
    @Override
    public List<ProtocolConverter> selectProtocolConverterList(ProtocolConverter protocolConverter) {
        return protocolConverterMapper.selectProtocolConverterList(protocolConverter);
    }

    /**
     * 新增协议转换
     */
    @Override
    public int insertProtocolConverter(ProtocolConverter protocolConverter) {
        List<ProtocolConverter> protocolConverters = protocolConverterMapper.selectProtocolConverterNameList(protocolConverter);
        if (!CollectionUtils.isEmpty(protocolConverters)) {
            throw new ServiceException("协议转换名称不能重复，请检查");
        }
        protocolConverter.setCreateTime(DateUtils.getNowDate());
        return protocolConverterMapper.insertProtocolConverter(protocolConverter);
    }

    /**
     * 修改协议转换
     */
    @Override
    public int updateProtocolConverter(ProtocolConverter protocolConverter) {
        List<ProtocolConverter> protocolConverters = protocolConverterMapper.selectProtocolConverterNameList(protocolConverter);
        if (!CollectionUtils.isEmpty(protocolConverters)) {
            throw new ServiceException("协议转换名称不能重复，请检查");
        }
        protocolConverter.setUpdateTime(DateUtils.getNowDate());
        if (protocolConverter.getUpdateType() == 2) {
            redisService.setCacheObject(CacheConstants.getProtocolCacheKey(protocolConverter.getConvertKey()), protocolConverter.getScriptContent());
        }
        return protocolConverterMapper.updateProtocolConverter(protocolConverter);
    }

    /**
     * 批量删除协议转换
     */
    @Override
    public int deleteProtocolConverterByIds(Long[] ids) {
        return protocolConverterMapper.deleteProtocolConverterByIds(ids);
    }

    /**
     * 删除协议转换信息
     */
    @Override
    public int deleteProtocolConverterById(Long id) {
        return protocolConverterMapper.deleteProtocolConverterById(id);
    }

    @Override
    public int updateStatusById(Long id, Integer status) {
        ProtocolConverter protocolConverter = protocolConverterMapper.selectProtocolConverterById(id);
        // 删除缓存
        if (status == 1) {
            redisService.deleteObject(CacheConstants.getProtocolCacheKey(protocolConverter.getConvertKey()));
        } else {
            // 更新缓存
            redisService.setCacheObject(CacheConstants.getProtocolCacheKey(protocolConverter.getConvertKey()), protocolConverter.getScriptContent());
        }
        return protocolConverterMapper.updateStatusById(id, status);
    }
}