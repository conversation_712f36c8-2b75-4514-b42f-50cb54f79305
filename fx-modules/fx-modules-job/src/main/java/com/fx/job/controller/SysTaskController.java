package com.fx.job.controller;

import com.fx.common.core.enums.ResultEnum;
import com.fx.common.core.exception.job.TaskException;
import com.fx.common.core.utils.SecurityUtils;
import com.fx.common.core.web.controller.BaseController;
import com.fx.common.core.web.domain.AjaxResult;
import com.fx.common.core.web.page.TableDataInfo;
import com.fx.common.log.annotation.Log;
import com.fx.common.log.enums.BusinessType;
import com.fx.common.security.annotation.PreAuthorize;
import com.fx.job.api.domain.SysTask;
import com.fx.job.service.ISysTaskService;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 批量下发调度任务信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/task")
public class SysTask<PERSON>ontroller extends BaseController {
    @Autowired
    private ISysTaskService taskService;

    /**
     * 查询批量下发任务列表
     */
    @PreAuthorize(hasPermi = "monitor:task:list")
    @GetMapping("/list")
    public TableDataInfo list(SysTask sysTask) {
        startPage();
        List<SysTask> list = taskService.selectTaskList(sysTask);
        return getDataTable(list);
    }

    /**
     * 获取批量下发任务详细信息
     */
    @PreAuthorize(hasPermi = "monitor:task:query")
    @GetMapping(value = "/{taskId}")
    public AjaxResult getInfo(@PathVariable("taskId") Long taskId) {
        return AjaxResult.success(taskService.selectTaskById(taskId));
    }

    /**
     * 新增批量下发任务
     */
    @PreAuthorize(hasPermi = "monitor:task:add")
    @Log(title = "批量下发任务新增" , businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysTask task) throws SchedulerException, TaskException {
        task.setCreateBy(SecurityUtils.getUsername());
        return AjaxResult.success(taskService.insertTask(task));
    }

    /**
     * 修改批量下发任务
     */
    @PreAuthorize(hasPermi = "monitor:task:edit")
    @Log(title = "批量下发任务修改" , businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysTask task) throws SchedulerException, TaskException {

        task.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(taskService.updateTask(task));
    }

    /**
     * 删除批量下发任务
     */
    @PreAuthorize(hasPermi = "monitor:task:remove")
    @Log(title = "批量下发任务删除" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskId}")
    public AjaxResult remove(@PathVariable Long taskId) throws SchedulerException, TaskException {
        taskService.deleteTaskById(taskId);
        return AjaxResult.success();
    }

    /**
     * 批量下发任务执行
     */
//    @PreAuthorize(hasPermi = "monitor:task:execute")
    @Log(title = "批量下发任务执行" , businessType = BusinessType.OTHER)
    @GetMapping("/execute")
    public AjaxResult execute(SysTask task) throws SchedulerException, TaskException {
        AjaxResult result = taskService.executeTask(task);
        if(ResultEnum.FAIL.getCode()==(int)result.get(AjaxResult.CODE_TAG)){
            return AjaxResult.error((String) result.get(AjaxResult.MSG_TAG));
        }else{
            return AjaxResult.success();
        }
    }

}
