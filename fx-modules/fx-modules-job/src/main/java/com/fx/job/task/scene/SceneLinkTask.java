package com.fx.job.task.scene;

import com.fx.rule.api.RemoteRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> feng
 * @version 1.0
 * @Desc
 * @date 2023/04/24
 */
@Component("sceneLinkTask")
@Slf4j
public class SceneLinkTask {

    @Autowired
    private RemoteRuleService remoteRuleService;

    public void createRuleConditions(Integer ruleId) {
        StopWatch watch = new StopWatch();
        watch.start();
        if (log.isInfoEnabled()) {
            log.info("解析规则条件定时任务开始，参数：{}" , ruleId);
        }
        remoteRuleService.executeSceneByRuleId(ruleId.longValue());
        //1.调用link模块获取当前规则对应的详细信息
        //2.在link模块组合查询条件
        //3.掉用tdengine模块查询数据
        //4.返回Link模块，获取消息转换器，将数据转换为三方平台需要的格式，通过http协议，将数据发送出去
        watch.stop();
        if (log.isInfoEnabled()) {
            log.info("解析规则条件定时任务结束，耗时(millisecond)：{}" , watch.getTime());
        }
    }

}
