package com.fx.rule.service.listener;

import com.fx.rule.service.expression.Expression;
import org.apache.commons.lang3.StringUtils;

import java.net.URLDecoder;
import java.util.Map;

public class Parameter {

    private String type;
    private String identifier;
    private Object value;
    private String comparator;

    public boolean matches(Map<?, ?> dataMap) throws Exception {
        //任意匹配
        if ("*".equals(identifier)) {
            return true;
        }
        //存在参数或无参数条件，值任意匹配
        if (StringUtils.isBlank(comparator)) {
            return false;
        }
        String decode = URLDecoder.decode(comparator, "UTF-8");
        if ((StringUtils.isBlank(identifier) || dataMap.containsKey(identifier))
                && "*".equals(decode)) {
            return true;
        }

        Object left = dataMap.get(identifier);
        if (left == null) {
            return false;
        }
        return Expression.eval(decode, left, value);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public String getComparator() {
        return comparator;
    }

    public void setComparator(String comparator) {
        this.comparator = comparator;

    }

}
