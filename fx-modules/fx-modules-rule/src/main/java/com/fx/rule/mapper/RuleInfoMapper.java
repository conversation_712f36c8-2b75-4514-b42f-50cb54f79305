package com.fx.rule.mapper;

import com.fx.rule.api.domain.RuleInfo;
import com.fx.rule.api.domain.rule.RuleRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 规则信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-20
 */
public interface RuleInfoMapper {
    /**
     * 查询规则信息
     *
     * @param id 规则信息主键
     * @return 规则信息
     */
    public RuleInfo selectRuleInfoById(Long id);

    /**
     * 查询规则信息列表
     *
     * @param ruleInfo 规则信息
     * @return 规则信息集合
     */
    public List<RuleInfo> selectRuleInfoList(RuleInfo ruleInfo);

    /**
     * 查询所有规则信息列表
     *
     * @return 规则信息集合
     */
    public List<RuleInfo> selectAllRuleInfoList(Map<String, Object> paramMap);

    /**
     * 新增规则信息
     *
     * @param ruleInfo 规则信息
     * @return 结果
     */
    public int insertRuleInfo(RuleInfo ruleInfo);

    /**
     * 修改规则信息
     *
     * @param ruleInfo 规则信息
     * @return 结果
     */
    public int updateRuleInfo(RuleInfo ruleInfo);

    /**
     * 删除规则信息
     *
     * @param id 规则信息主键
     * @return 结果
     */
    public int deleteRuleInfoById(Long id);

    /**
     * 批量删除规则信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRuleInfoByIds(Long[] ids);

    /**
     * 修改规则信息
     *
     * @param id     产品Id
     * @param status 状态
     * @return 结果
     */
    public int updateRuleInfoStatus(@Param(value = "id") Long id, @Param(value = "status") Integer status);

    /**
     * 查询数据流转名称列表
     *
     * @param ruleInfo 数据流转
     * @return 产品模型集合
     */
    public List<RuleInfo> selectRuleNameList(RuleInfo ruleInfo);

    /**
     * 查询规则记录列表
     *
     * @param ruleRecord 规则记录
     * @return 规则记录集合
     */
    public List<RuleRecord> selectRuleRecordList(RuleRecord ruleRecord);

    /**
     * 新增规则记录
     *
     * @param ruleRecord 规则记录
     * @return 结果
     */
    public int insertRuleRecord(RuleRecord ruleRecord);

    /**
     * 删除规则记录
     *
     * @param ids 需要删除的数据主键集合
     */
    int deleteRuleRecordByRecordIds(Long[] ids);

    /**
     * 删除小于开始时间的规则记录
     *
     * @param startTime 开始时间
     */
    int deleteRuleRecordByStartTime(Date startTime);
}
