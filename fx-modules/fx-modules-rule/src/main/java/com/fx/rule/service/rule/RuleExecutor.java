package com.fx.rule.service.rule;

import cn.hutool.json.JSONUtil;
import com.fx.common.core.domain.ThingModelMessage;
import com.fx.common.core.utils.JsonUtil;
import com.fx.common.redis.service.RedisService;
import com.fx.link.api.domain.device.entity.SceneWarnDto;
import com.fx.rule.api.Action;
import com.fx.rule.api.Filter;
import com.fx.rule.api.Listener;
import com.fx.rule.api.domain.rule.Rule;
import com.fx.rule.api.domain.rule.RuleRecord;
import com.fx.rule.mapper.RuleInfoMapper;
import com.fx.rule.service.action.DeviceAction;
import com.fx.tdengine.api.RemoteTdEngineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 规则执行器
 */
@Component
@Slf4j
public class RuleExecutor {

    @Resource
    private RuleInfoMapper ruleInfoMapper;
    @Resource
    private RemoteTdEngineService remoteTdEngineService;
    @Resource
    private RuleSendManager ruleSendManager;
    @Resource
    private RedisService redisService;

    /**
     * 执行规则
     *
     * @param message 消息
     * @param rule    规则
     * @throws Exception 异常
     */
    public void execute(ThingModelMessage message, Rule rule) throws Exception {
        // 1. 产品或设备是否匹配
        if (doListeners(message, rule)) {
            log.info("开始执行 [ rule: {}, {}，deviceKey：{} ]", rule.getName(), rule.getId(), message.getDeviceKey());
        } else {
            log.info("监听器不匹配 , [ rule: {}, {}，deviceKey：{} ]", rule.getName(), rule.getId(), message.getDeviceKey());
            return;
        }

        RuleRecord ruleLog = createRuleRecord(rule);

        try {
            // 2. 场景联动上下线触发时不走执行条件，直接执行设备动作
            if (!ThingModelMessage.TYPE_STATE.equals(message.getType())) {
                if (!doFilters(message, rule)) {
                    ruleLog.setStatus(RuleRecord.RuleRecordState.UNMATCHED_FILTER.getCode());
                    log.info("过滤器不匹配 , rule:{},{},deviceKey:{}", rule.getName(), rule.getId(), message.getDeviceKey());
                    return;
                }
                ruleLog.setStatus(RuleRecord.RuleRecordState.MATCHED_FILTER.getCode());
            }

            // 3. 执行动作返回执行内容
            List<String> results = doActions(rule, message);
            // 4. 保存动作内容和状态
            ruleLog.setActions(JsonUtil.toJsonString(results));
            ruleLog.setStatus(RuleRecord.RuleRecordState.EXECUTED_ACTION.getCode());
            log.info("规则执行完成,id:{}", rule.getId());
        } catch (Throwable e) {
            log.error("规则执行错误,id:{}", rule.getId(), e);
            ruleLog.setStatus(RuleRecord.RuleRecordState.ERROR_EXECUTED_ACTION.getCode());
            ruleLog.setReason(e.toString());
        } finally {
            ruleLog.setEndTime(new Date());
            ruleInfoMapper.insertRuleRecord(ruleLog);
        }
    }

    /**
     * 创建规则记录
     *
     * @param rule 规则
     * @return 规则记录
     */
    private RuleRecord createRuleRecord(Rule rule) {
        RuleRecord ruleLog = new RuleRecord();
        ruleLog.setStartTime(new Date());
        ruleLog.setRuleId(Integer.valueOf(rule.getId()));
        ruleLog.setStatus(RuleRecord.RuleRecordState.MATCHED_LISTENER.getCode());
        ruleLog.setType(rule.getType());
        return ruleLog;
    }

    /**
     * 执行规则
     *
     * @param rule 规则
     */
    public void execute(Rule rule) {
        log.info("开始执行规则 {},id:{}", rule.getName(), rule.getId());

        RuleRecord ruleLog = createRuleRecord(rule);

        try {
            // 1. 检查过滤器是否匹配
            if (doFilters(null, rule)) {
                ruleLog.setStatus(RuleRecord.RuleRecordState.UNMATCHED_FILTER.getCode());
                log.info("过滤器与适当的内容不匹配,rule:{},{}", rule.getId(), rule.getName());
                return;
            }
            ruleLog.setStatus(RuleRecord.RuleRecordState.MATCHED_FILTER.getCode());

            // 2. 执行动作并返回执行内容
            List<String> results = doActions(rule, null);
            // 3. 保存动作内容和状态
            ruleLog.setActions(JsonUtil.toJsonString(results));
            ruleLog.setStatus(RuleRecord.RuleRecordState.EXECUTED_ACTION.getCode());
            log.info("规则执行完成,id:{}", rule.getId());
        } catch (Throwable e) {
            log.error("规则执行错误,id:{}", rule.getId(), e);
            ruleLog.setStatus(RuleRecord.RuleRecordState.ERROR_EXECUTED_ACTION.getCode());
            ruleLog.setReason(e.toString());
        } finally {
            ruleLog.setEndTime(new Date());
            ruleInfoMapper.insertRuleRecord(ruleLog);
        }
    }

    /**
     * 执行单个规则
     *
     * @param rule 规则
     */
    public void executeOne(Rule rule) {
        log.info("开始执行规则 {},id:{}", rule.getName(), rule.getId());

        RuleRecord ruleLog = createRuleRecord(rule);

        try {
            // 1. 执行动作并返回执行内容
            List<String> results = doActions(rule, null);
            // 2. 保存动作内容和状态
            ruleLog.setActions(JsonUtil.toJsonString(results));
            ruleLog.setStatus(RuleRecord.RuleRecordState.EXECUTED_ACTION.getCode());
            log.info("规则执行完成,id:{}", rule.getId());
        } catch (Throwable e) {
            log.error("规则执行错误,id:{}", rule.getId(), e);
            ruleLog.setStatus(RuleRecord.RuleRecordState.ERROR_EXECUTED_ACTION.getCode());
            ruleLog.setReason(e.toString());
        } finally {
            ruleLog.setEndTime(new Date());
            ruleInfoMapper.insertRuleRecord(ruleLog);
        }
    }

    /**
     * 执行监听器 只要有一个监听器匹配到数据即可
     *
     * @param message 消息 用于监听器匹配
     * @param rule    规则 用于获取监听器
     * @return 是否通过 true 通过 false 不通过
     * @throws Exception 异常 有异常则不通过
     */
    private boolean doListeners(ThingModelMessage message, Rule rule) throws Exception {
        List<Listener<?>> listeners = rule.getListeners();
        for (Listener<?> listener : listeners) {
            if (listener.execute(message)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 执行过滤器
     *
     * @param message 消息
     * @param rule    规则
     * @return 是否通过 true 通过 false 不通过
     * @throws Exception 异常
     */
    private boolean doFilters(ThingModelMessage message, Rule rule) throws Exception {
        Filter<?> filter = rule.getFilter();
        filter.setRemoteTdEngineService(remoteTdEngineService);
        return filter.execute(message);
    }

    /**
     * 执行动作
     *
     * @param rule 规则
     * @param msg  消息
     * @return 执行结果列表
     * @throws Exception 异常
     */
    public List<String> doActions(Rule rule, ThingModelMessage msg) throws Exception {
        List<ThingModelMessage> actionList = new ArrayList<>();
        List<String> resultList = new ArrayList<>();
        for (Action<?> action : rule.getActions()) {
            switch (action.getType()) {
                case "2":
                    SceneWarnDto sceneWarnDto = ruleSendManager.warnSend(rule, msg);
                    resultList.add(JSONUtil.toJsonStr(sceneWarnDto));
                    break;
                case "Http":
                    DeviceAction deviceActionHttp = ruleSendManager.httpSend(rule, action);
                    resultList.add(JSONUtil.toJsonStr(deviceActionHttp));
                    break;
                case "Mqtt":
                    DeviceAction deviceActionMqtt = ruleSendManager.mqttSend(rule, action);
                    resultList.add(JSONUtil.toJsonStr(deviceActionMqtt));
                    break;
                case "MySql":
                    DeviceAction deviceActionMySql = ruleSendManager.mysqlSend(rule, action);
                    resultList.add(JSONUtil.toJsonStr(deviceActionMySql));
                    break;
                default:
                    // 其他设备输出
                    resultList.addAll(action.execute(actionList, msg));
                    break;
            }
        }
        return resultList;
    }
}