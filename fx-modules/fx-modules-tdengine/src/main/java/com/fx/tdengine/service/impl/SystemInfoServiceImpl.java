/*
 * +----------------------------------------------------------------------
 * | Copyright (c) 奇特物联 2021-2022 All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed 未经许可不能去掉「奇特物联」相关版权
 * +----------------------------------------------------------------------
 * | Author: <EMAIL>
 * +----------------------------------------------------------------------
 */
package com.fx.tdengine.service.impl;

import com.fx.tdengine.api.domain.SelectDto;
import com.fx.tdengine.api.domain.SysInfo;
import com.fx.tdengine.api.domain.SysInfoVo;
import com.fx.tdengine.mapper.SystemInfoMapper;
import com.fx.tdengine.service.SystemInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SystemInfoServiceImpl implements SystemInfoService {

    @Autowired
    private SystemInfoMapper systemInfoMapper;


    @Override
    public void add(SysInfo msg) {
        systemInfoMapper.saveSystemInfo(msg);
    }

    @Override
    public List<SysInfoVo> findSysInfo(SelectDto selectDto) {
        return systemInfoMapper.findSysInfo(selectDto);
    }
}
