package com.fx.tdengine.service.impl;

import com.fx.common.core.domain.ThingModelMessage;
import com.fx.common.core.utils.JsonUtil;
import com.fx.common.core.utils.StringUtils;
import com.fx.common.security.service.TokenService;
import com.fx.system.api.domain.SysUser;
import com.fx.system.api.model.LoginUser;
import com.fx.tdengine.api.domain.MessageCountVo;
import com.fx.tdengine.api.domain.SelectDto;
import com.fx.tdengine.dm.TimeData;
import com.fx.tdengine.mapper.ThingModelMessageMapper;
import com.fx.tdengine.service.ThingModelMessageService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class ThingModelMessageServiceImpl implements ThingModelMessageService {

    @Resource
    private ThingModelMessageMapper thingModelMessageMapper;

    @Resource
    private TokenService tokenService;

    public List<ThingModelMessage> findByTypeAndIdentifier(String deviceKey, String type,
                                                           String identifier) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("deviceKey", deviceKey);
        if (StringUtils.isNotBlank(type)) {
            paramMap.put("type", type);
        }
        if (StringUtils.isNotBlank(identifier)) {
            paramMap.put("identifier", identifier);
        }
        List<ThingModelMessage> ruleLogs = thingModelMessageMapper.queryRuleLogs(paramMap);

        return ruleLogs;
    }

    @Override
    public List<TimeData> getDeviceMessageStatsWithUid(String uid, long start, long end) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("start", start);
        paramMap.put("end", end);

        if (StringUtils.isNotEmpty(uid)) {
            paramMap.put("uid", uid);
        }
        List<TimeData> timeDataList = thingModelMessageMapper.getDeviceMessageStatsWithUid(paramMap);
        return timeDataList;
    }

    @Override
    public List<MessageCountVo> getDeviceMessage(SelectDto selectDto) {
        if (dataAccess(selectDto)) {
            return new ArrayList<>();
        }
        return thingModelMessageMapper.getDeviceMessage(selectDto);
    }

    @Override
    public boolean dataAccess(SelectDto selectDto) {
        LoginUser loginUser = tokenService.getLoginUser();
        if (loginUser == null || loginUser.getSysUser() == null) {
            return true;
        }
        SysUser currentUser = loginUser.getSysUser();
        if (currentUser.isAdmin() || currentUser.isSuper()) {
            selectDto.setOrgIds(null);
        } else {
            Set<Integer> orgIds = new HashSet<>(loginUser.getOrgIds());
            if (orgIds.isEmpty()) {
                return true;
            }
            selectDto.setOrgIds(orgIds);
        }
        return false;
    }


    @Override
    public void add(ThingModelMessage msg) {
        //使用deviceId做表名
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("tdTableName", "thing_model_message_" + msg.getDeviceKey().toLowerCase());
        paramMap.put("mid", msg.getMid());
        paramMap.put("tags", msg.getDeviceKey());
        paramMap.put("deviceName", msg.getDeviceName());
        paramMap.put("productKey", msg.getProductKey());
        paramMap.put("uid", msg.getUid());
        paramMap.put("type", msg.getType());
        paramMap.put("identifier", msg.getIdentifier());
        paramMap.put("code", msg.getCode());
        paramMap.put("data", msg.getData() == null ? "{}" : JsonUtil.toJsonString(msg.getData()));
        paramMap.put("time", msg.getTime());
        paramMap.put("reportTime", msg.getOccurred());
        thingModelMessageMapper.saveThingModelMessage(paramMap);
    }

    @Override
    public long count(SelectDto selectDto) {
        if (dataAccess(selectDto)) {
            return 0;
        }
        return Optional.ofNullable(thingModelMessageMapper.queryCount(selectDto)).orElse(0L);
    }

}
