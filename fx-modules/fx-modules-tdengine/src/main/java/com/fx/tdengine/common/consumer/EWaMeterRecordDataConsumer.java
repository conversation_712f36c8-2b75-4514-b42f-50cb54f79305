package com.fx.tdengine.common.consumer;

import com.alibaba.fastjson.JSONObject;
import com.fx.common.core.constant.CacheConstants;
import com.fx.common.core.constant.TableNameConstants;
import com.fx.common.core.utils.DateUtils;
import com.fx.common.core.utils.StringUtils;
import com.fx.common.redis.service.RedisService;
import com.fx.common.rocketmq.constant.ConsumerGroupConstant;
import com.fx.common.rocketmq.constant.ConsumerTopicConstant;
import com.fx.link.api.domain.device.entity.Device;
import com.fx.link.api.domain.product.entity.ThingModelVo;
import com.fx.tdengine.dm.TableData;
import com.fx.tdengine.mapper.TdDeviceMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * EWaMeterRecordDataConsumer 是一个消费者类，用于处理接收到的电表记录数据。
 * 它实现了 RocketMQListener 接口，用于处理接收到的消息。
 * 它使用 TdDeviceMapper 和 RedisService 来处理接收到的数据。
 * 它使用 @Slf4j 注解，为类提供日志记录功能。
 * 它使用 @Component 注解，使其成为 Spring 的组件，可以通过自动装配使用。
 * 它使用 @RocketMQMessageListener 注解，设置消费者组和主题。
 *
 * <AUTHOR>
 * @since 2022-12-27
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = ConsumerGroupConstant.TDENGINE_CONSUMER_GROUP_EWA_DATA_SAVE, topic = ConsumerTopicConstant.EA_METER_RECORD_SAVE)
public class EWaMeterRecordDataConsumer implements RocketMQListener {

    @Resource
    private TdDeviceMapper tdDeviceMapper;
    @Resource
    private RedisService redisService;

    /**
     * 当接收到消息时的处理方法。
     * 如果消息为空，则不进行处理。
     * 如果消息不为空，则解析消息，并将数据保存到数据库和Redis中。
     *
     * @param message 接收到的消息对象。
     */
    @Override
    public void onMessage(Object message) {
        if (StringUtils.isNull(message)) {
            log.error("消息为空，不处理");
            return;
        }
        JSONObject recodeMessage = JSONObject.parseObject(String.valueOf(message));
        Object meterReadInfos = recodeMessage.get("meterReadInfos");
        Object device = recodeMessage.get("device");
        Object thingModels = recodeMessage.get("thingModels");
        List<Map> eWaDataVOS = JSONObject.parseArray(String.valueOf(meterReadInfos), Map.class);
        List<Device> devices = JSONObject.parseArray(String.valueOf(device), Device.class);
        List<ThingModelVo> thingModels1 = JSONObject.parseArray(String.valueOf(thingModels), ThingModelVo.class);
        //1.通过表编号查询deviceKey和productKey
        //2.获取物模型字段
        //3.通过字段匹配值
        //4.存入tdengine
        for (Map map : eWaDataVOS) {
            List<String> schemaFieldList = new ArrayList<>();
            List<Object> schemaValueList = new ArrayList<>();
            List<Object> tagsValueList = new ArrayList<>();

            for (Device deviceInfo : devices) {
                if (map.get("mnum").equals(deviceInfo.getDeviceNo())) {
                    //获取设备旧属性
                    schemaFieldList.add("time");
                    Object o = map.get("ptime");
                    if (o == null) {
                        schemaValueList.add(System.currentTimeMillis());
                    } else {
                        Long aLong = DateUtils.string2MillisWithJdk8(o.toString(), "yyyy-MM-dd HH:mm:ss");
                        schemaValueList.add(aLong);
                    }
                    String propertyCacheKey = CacheConstants.getPropertyCacheKey(deviceInfo.getDeviceKey());
                    HashMap<String, Object> propertyMap = new HashMap<>();
                    for (ThingModelVo t : thingModels1) {
                        if (deviceInfo.getProductKey().equals(t.getProductKey())) {
                            List<ThingModelVo.Property> properties = t.getModel().getProperties();
                            for (ThingModelVo.Property property : properties) {
                                schemaFieldList.add(property.getIdentifier().toLowerCase());
                                schemaValueList.add(map.get(property.getIdentifier().toLowerCase()));
                                propertyMap.put(property.getIdentifier().toLowerCase(), map.get(property.getIdentifier().toLowerCase()));
                            }
                        }
                    }
                    redisService.setCacheObject(propertyCacheKey, propertyMap);
                    tagsValueList.add(deviceInfo.getDeviceKey());
                    //设置插入所需参数
                    TableData tableData = new TableData();
                    // 表名
                    tableData.setTableName(TableNameConstants.getDevicePropertyTableName(deviceInfo.getDeviceType(), deviceInfo.getProductKey(), deviceInfo.getDeviceKey()));
                    // 超表名
                    tableData.setSuperTableName(TableNameConstants.getProductPropertySTableName(deviceInfo.getDeviceType(), deviceInfo.getProductKey()));
                    // 字段名
                    tableData.setSchemaFieldList(schemaFieldList);
                    // 字段值
                    tableData.setSchemaValueList(schemaValueList);
                    // tag值
                    tableData.setTagsValueList(tagsValueList);
                    // 保存设备属性
                    tdDeviceMapper.saveData(tableData);
                }
            }
        }
    }
}