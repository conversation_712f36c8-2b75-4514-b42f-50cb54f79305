package com.fx.link.api.factory;

import com.fx.common.core.domain.R;
import com.fx.link.api.RemoteThingModelService;
import com.fx.link.api.domain.product.entity.ThingModel;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public class RemoteThingModelFallbackFactory implements FallbackFactory<RemoteThingModelService> {
    @Override
    public RemoteThingModelService create(Throwable cause) {
        return new RemoteThingModelService() {
            @Override
            public R<List<ThingModel>> findThingModeByProductKey(@RequestParam("productKey") String[] productKey) {
                return R.fail("查询物模型信息:" + cause.getMessage());
            }
        };
    }
}
